# جميع المكتبات المطلوبة لكود main.py والملفات المرتبطة

## 📋 تحليل شامل للمكتبات المستوردة

بناءً على فحص ملف `main.py` وجميع الملفات المرتبطة، إليك قائمة شاملة بجميع المكتبات المطلوبة:

## 🎯 المكتبات الأساسية (Core Libraries)

### 1. Python والإطار الأساسي
```ini
python3==3.11.6          # إصدار Python المطلوب
kivy==2.3.0              # إطار العمل الأساسي للواجهة
kivymd==1.1.1            # مكونات Material Design
pyjnius                  # للتفاعل مع Java/Android APIs
android                  # دعم منصة الأندرويد
```

## 🎵 مكتبات معالجة الصوت والوسائط

### 2. تشغيل ومعالجة الصوت
```ini
mutagen==1.47.0          # قراءة metadata للملفات الصوتية
pygame                   # تشغيل الصوت (مستخدم في main.py)
ffpyplayer               # تشغيل الصوت والفيديو (اختياري)
```

**الاستخدام في الكود:**
- `mutagen`: السطور 116-123, 145-147 في main.py
- `pygame`: السطر 41 في main.py (os.environ['KIVY_AUDIO'] = 'pygame')

## 🔤 مكتبات النص العربي

### 3. دعم اللغة العربية
```ini
python-bidi==0.4.2       # خوارزمية النص ثنائي الاتجاه
arabic-reshaper==3.0.0   # إعادة تشكيل النص العربي
```

**الاستخدام في الكود:**
- `arabic_utils.py`: السطور 14-15
- مستورد في `main.py`, `search_screen.py`, `download_screen.py`

## 🖼️ مكتبات معالجة الصور

### 4. الصور والرسومات
```ini
pillow==10.0.1           # معالجة وتحرير الصور
```

**الاستخدام في الكود:**
- السطر 48 في main.py: `os.environ['KIVY_TEXT'] = 'pil'`
- لمعالجة صور أغلفة الألبومات

## 🌐 مكتبات الشبكة والتحميل

### 5. الاتصال بالإنترنت والتحميل
```ini
requests==2.31.0         # طلبات HTTP
urllib3                  # مكتبة HTTP منخفضة المستوى
certifi                  # شهادات SSL
charset-normalizer       # تشفير النصوص
idna                     # معالجة أسماء النطاقات الدولية
```

**الاستخدام في الكود:**
- `download_manager.py`: السطر 13
- `search_screen.py`: للبحث في APIs

### 6. تحميل من YouTube
```ini
pytube                   # تحميل من YouTube (الطريقة الأولى)
yt-dlp                   # بديل أكثر استقرارًا لـ pytube
```

**الاستخدام في الكود:**
- `download_manager.py`: السطور 20, 28
- `search_screen.py`: السطور 39, 46

## 🔧 مكتبات النظام والأداء

### 7. مراقبة النظام والأداء
```ini
plyer==2.1.0             # الوصول لميزات النظام
psutil                   # مراقبة الأداء والذاكرة
numpy                    # معالجة البيانات الصوتية
```

**الاستخدام في الكود:**
- `performance_optimizer.py`: psutil لمراقبة الأداء
- `audio_enhancer.py`: numpy لمعالجة الصوت

## 🛠️ مكتبات الدعم والأدوات

### 8. أدوات مساعدة
```ini
six                      # توافق Python 2/3
setuptools               # أدوات التثبيت
wheel                    # تنسيق توزيع Python
```

## 📦 المكتبات المدمجة في Python (لا تحتاج تثبيت)

### مكتبات Python القياسية المستخدمة:
```python
import os                # إدارة النظام
import time              # الوقت والتوقيت
import logging           # السجلات
import traceback         # تتبع الأخطاء
import json              # معالجة JSON
import re                # التعبيرات النمطية
import subprocess        # تشغيل العمليات
import hashlib           # التشفير
import sys               # معلومات النظام
import threading         # المعالجة المتوازية
from io import BytesIO   # معالجة البيانات
from base64 import b64decode  # تشفير base64
from math import cos, sin, radians  # العمليات الرياضية
from urllib.parse import urlparse, unquote  # معالجة URLs
from queue import Queue  # الطوابير
import tempfile          # الملفات المؤقتة
```

## 🎯 قائمة buildozer.spec الكاملة

### للنسخة الكاملة (مع جميع الميزات):
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,mutagen==1.47.0,pyjnius,android,python-bidi==0.4.2,arabic-reshaper==3.0.0,pillow==10.0.1,requests==2.31.0,plyer==2.1.0,certifi,urllib3,charset-normalizer,idna,numpy,pygame,psutil,six,setuptools,wheel,pytube,yt-dlp
```

### للنسخة المبسطة (بدون ميزات متقدمة):
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,mutagen==1.47.0,pyjnius,android,python-bidi==0.4.2,arabic-reshaper==3.0.0,pillow==10.0.1,requests==2.31.0,plyer==2.1.0
```

### للنسخة الأساسية (للاختبار فقط):
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,pyjnius,android
```

## 🚨 المكتبات التي قد تسبب مشاكل في البناء

### مكتبات تتطلب تجميع C/C++:
- `numpy` - قد تفشل في التجميع
- `psutil` - قد تفشل في التجميع
- `pygame` - قد تتطلب SDL

### مكتبات قد تكون غير مستقرة:
- `pytube` - قد تفشل مع تحديثات YouTube
- `yt-dlp` - كبيرة الحجم
- `ffpyplayer` - معقدة التجميع

## 💡 توصيات للبناء الناجح

### 1. ابدأ بالنسخة الأساسية:
```bash
cp buildozer_basic_test.spec buildozer.spec
# عدل requirements إلى:
requirements = python3,kivy,kivymd,pyjnius,android
```

### 2. أضف المكتبات تدريجياً:
```bash
# المرحلة 1: الصوت
requirements = python3,kivy,kivymd,mutagen,pyjnius,android

# المرحلة 2: العربية
requirements = python3,kivy,kivymd,mutagen,pyjnius,android,python-bidi,arabic-reshaper

# المرحلة 3: الشبكة
requirements = python3,kivy,kivymd,mutagen,pyjnius,android,python-bidi,arabic-reshaper,requests

# المرحلة 4: الصور
requirements = python3,kivy,kivymd,mutagen,pyjnius,android,python-bidi,arabic-reshaper,requests,pillow
```

### 3. اختبر بعد كل إضافة:
```bash
buildozer android clean
buildozer android debug
```

## 📱 الأذونات المطلوبة

```ini
android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,MANAGE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS,RECORD_AUDIO,ACCESS_WIFI_STATE,CHANGE_WIFI_STATE,VIBRATE
```

هذه القائمة الشاملة تغطي جميع المكتبات المطلوبة لتشغيل الكود بجميع ميزاته.
