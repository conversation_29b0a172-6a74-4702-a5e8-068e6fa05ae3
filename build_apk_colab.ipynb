{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github"}, "source": ["# بناء تطبيق مشغل الموسيقى العربي لنظام أندرويد\n", "\n", "هذا الملف يساعدك على تحويل تطبيق مشغل الموسيقى العربي إلى ملف APK باستخدام Google Colab."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_requirements", "cellView": "form"}, "outputs": [], "source": ["# @title تثبيت المتطلبات الأساسية وإعداد البيئة { display-mode: \"form\" }\n", "\n", "# تثبيت المتطلبات الأساسية\n", "!pip install buildozer cython==0.29.33 pillow\n", "\n", "# تثبيت متطلبات النظام\n", "!apt-get update\n", "!apt-get install -y python3-pip build-essential git python3 python3-dev ffmpeg libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev libsdl2-ttf-dev libportmidi-dev libswscale-dev libavformat-dev libavcodec-dev zlib1g-dev\n", "\n", "# تثبيت متطلبات Android\n", "!apt-get install -y openjdk-11-jdk\n", "!apt-get install -y autoconf automake libtool\n", "\n", "# تثبيت متطلبات Buildozer\n", "!pip install --upgrade Cython==0.29.33\n", "\n", "# تثبيت أدوات إضافية\n", "!apt-get install -y libltdl-dev libffi-dev ccache\n", "\n", "print(\"تم تثبيت جميع المتطلبات بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive", "cellView": "form"}, "outputs": [], "source": ["# @title ربط Google Drive { display-mode: \"form\" }\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "print(\"تم ربط Google Drive بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "extract_project", "cellView": "form"}, "outputs": [], "source": ["# @title استخراج ملفات المشروع { display-mode: \"form\" }\n", "import os\n", "import zipfile\n", "\n", "# مسار ملف ZIP في Google Drive - قم بتغيير هذا المسار\n", "zip_path = \"/content/drive/MyDrive/MusicPlayer.zip\"  # قم بتغيير هذا المسار\n", "\n", "# إنشاء مجلد للمشروع\n", "!mkdir -p /content/musicplayer\n", "\n", "# استخراج الملفات\n", "with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "    zip_ref.extractall('/content/musicplayer')\n", "\n", "# الانتقال إلى مجلد المشروع\n", "%cd /content/musicplayer\n", "\n", "print(\"تم استخراج ملفات المشروع بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_buildozer_spec", "cellView": "form"}, "outputs": [], "source": ["# @title إنشاء ملف buildozer.spec { display-mode: \"form\" }\n", "\n", "buildozer_spec = '''\n", "[app]\n", "title = Arabic Music Player\n", "package.name = arabicmusicplayer\n", "package.domain = org.arabicmusicplayer\n", "source.dir = .\n", "source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json\n", "source.include_patterns = fonts/*, images/*, album_covers/*, *.py, *.kv\n", "source.exclude_dirs = __pycache__, bin, .buildozer, venv\n", "source.exclude_patterns = *.pyc, *.pyo, *.pyd, *.so, *.dll, *.egg-info\n", "version = 1.0\n", "requirements = python3,kivy==2.3.0,kivymd==1.1.1,mutagen,pyjnius,android,python-bidi,arabic-reshaper,pillow,ffpyplayer,requests\n", "orientation = portrait\n", "osx.python_version = 3\n", "fullscreen = 1\n", "presplash.filename = images/default_album_cover.png\n", "icon.filename = images/default_album_cover.png\n", "\n", "# Android-specific\n", "android.api = 33\n", "android.minapi = 21\n", "android.ndk = 25b\n", "android.ndk_api = 21\n", "android.archs = armeabi-v7a,arm64-v8a\n", "android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,MANAGE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK\n", "android.allow_backup = 1\n", "android.gradle_repositories = https://jitpack.io\n", "android.copy_libs = 1\n", "android.private_storage = true\n", "android.accept_sdk_license = True\n", "android.logcat_filters = *:S python:D\n", "android.entrypoint = main.py\n", "android.numeric_version = 1\n", "android.skip_update = True\n", "\n", "# Intent filters for URL sharing\n", "android.manifest.intent_filters =\n", "    [\n", "        {\n", "            \"action\": [\"android.intent.action.SEND\"],\n", "            \"category\": [\"android.intent.category.DEFAULT\"],\n", "            \"data_mimetypes\": [\"text/plain\"],\n", "            \"meta_data\": [\n", "                {\"name\": \"android.service.chooser.chooser_target_service\", \"value\": \".ShareTargetService\"}\n", "            ],\n", "            \"alias\": \"Share URL to Music Player\"\n", "        },\n", "        {\n", "            \"action\": [\"android.intent.action.VIEW\"],\n", "            \"category\": [\"android.intent.category.DEFAULT\", \"android.intent.category.BROWSABLE\"],\n", "            \"data_schemes\": [\"http\", \"https\"],\n", "            \"data_path_prefixes\": [\"/\"],\n", "            \"data_host_patterns\": [\".*youtube\\\\.com.*\", \".*youtu\\\\.be.*\", \".*soundcloud\\\\.com.*\"]\n", "        },\n", "        {\n", "            \"action\": [\"android.intent.action.PROCESS_TEXT\"],\n", "            \"category\": [\"android.intent.category.DEFAULT\"],\n", "            \"data_mimetypes\": [\"text/plain\"]\n", "        }\n", "    ]\n", "\n", "# إصلاح أخطاء متعلقة ب Libffi\n", "p4a.branch = develop\n", "\n", "# لمنع مشاكل ccache\n", "android.use_ccache = 0\n", "\n", "# تحسينات الأداء\n", "android.gradle_dependencies = com.google.android.material:material:1.4.0, androidx.core:core:1.6.0, androidx.appcompat:appcompat:1.3.1\n", "android.add_jars = libs/*.jar\n", "android.add_aars = libs/*.aar\n", "android.wakelock = True\n", "\n", "# تحسين حجم التطبيق\n", "android.enable_androidx = True\n", "android.enable_asset_packing = True\n", "android.enable_manifest_placeholders = True\n", "android.enable_gradle_daemon = True\n", "\n", "# تحسينات إضافية\n", "android.extra_manifest_xml = <meta-data android:name=\"android.max_aspect\" android:value=\"2.1\" />\n", "android.meta_data = com.google.android.gms.version=@integer/google_play_services_version\n", "android.backup_rules = @xml/backup_rules\n", "\n", "# تحسينات البناء\n", "p4a.hook = \n", "p4a.force_build = True\n", "p4a.bootstrap = sdl2\n", "p4a.dist_name = arabicmusicplayer\n", "p4a.release_artifact = apk\n", "\n", "[buildozer]\n", "log_level = 2\n", "warn_on_root = 1\n", "'''\n", "\n", "# كتابة ملف buildozer.spec\n", "with open('buildozer.spec', 'w') as f:\n", "    f.write(buildozer_spec)\n", "\n", "print(\"تم إنشاء ملف buildozer.spec بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_libs_dir", "cellView": "form"}, "outputs": [], "source": ["# @title إنشاء مجلد libs { display-mode: \"form\" }\n", "!mkdir -p libs\n", "print(\"تم إنشاء مجلد libs بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "build_apk", "cellView": "form"}, "outputs": [], "source": ["# @title بناء ملف APK { display-mode: \"form\" }\n", "# منع انقطاع الاتصال\n", "from IPython.display import display, Javascript\n", "display(Javascript('''\n", "function ClickConnect(){\n", "    console.log(\"Working\"); \n", "    document.querySelector(\"colab-toolbar-button#connect\").click() \n", "}\n", "setInterval(ClickConnect, 60000)\n", "'''))\n", "\n", "# تعيين متغيرات البيئة\n", "!export PATH=$PATH:~/.local/bin/\n", "\n", "# بناء ملف APK مع حفظ السجلات\n", "!buildozer -v android debug > build_log.txt 2>&1\n", "\n", "# عرض نتيجة البناء\n", "!cat build_log.txt | tail -n 50\n", "\n", "print(\"\\nتم الانتهاء من عملية البناء!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "copy_apk", "cellView": "form"}, "outputs": [], "source": ["# @title نسخ ملف APK إلى Google Drive { display-mode: \"form\" }\n", "import os\n", "import shutil\n", "\n", "# التحقق من وجود ملف APK\n", "apk_path = '/content/musicplayer/bin/arabicmusicplayer-1.0-debug.apk'\n", "if not os.path.exists(apk_path):\n", "    apk_path = '/content/musicplayer/bin/musicplayer-1.0-debug.apk'\n", "\n", "if os.path.exists(apk_path):\n", "    # إنشاء مجلد في Google Drive\n", "    output_dir = '/content/drive/MyDrive/ArabicMusicPlayerAPK'\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # نسخ ملف APK\n", "    shutil.copy(apk_path, os.path.join(output_dir, 'ArabicMusicPlayer.apk'))\n", "    \n", "    # نسخ سجلات البناء\n", "    if os.path.exists('build_log.txt'):\n", "        shutil.copy('build_log.txt', os.path.join(output_dir, 'build_log.txt'))\n", "    \n", "    print(f\"تم نسخ ملف APK إلى: {output_dir}/ArabicMusicPlayer.apk\")\n", "else:\n", "    print(\"لم يتم العثور على ملف APK! تحقق من عملية البناء.\")\n", "    \n", "    # نسخ سجلات البناء للتحقق من الأخطاء\n", "    if os.path.exists('build_log.txt'):\n", "        output_dir = '/content/drive/MyDrive/ArabicMusicPlayerAPK'\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        shutil.copy('build_log.txt', os.path.join(output_dir, 'build_log.txt'))\n", "        print(f\"تم نسخ سجلات البناء إلى: {output_dir}/build_log.txt\")\n", "        \n", "    # التحقق من سجلات Buildozer\n", "    buildozer_log = '.buildozer/logs/buildozer.log'\n", "    if os.path.exists(buildozer_log):\n", "        output_dir = '/content/drive/MyDrive/ArabicMusicPlayerAPK'\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        shutil.copy(buildozer_log, os.path.join(output_dir, 'buildozer.log'))\n", "        print(f\"تم نسخ سجلات Buildozer إلى: {output_dir}/buildozer.log\")"]}], "metadata": {"colab": {"name": "build_arabic_music_player_apk.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}