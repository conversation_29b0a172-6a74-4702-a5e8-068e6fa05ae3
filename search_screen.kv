#:import dp kivy.metrics.dp
#:import sp kivy.metrics.sp

<SearchResultItem>:
    font_style: "Body1"
    ripple_behavior: True  # Add ripple effect on tap

    # Add a subtle background color when selected
    canvas.before:
        Color:
            rgba: app.root.get_primary_color()[0], app.root.get_primary_color()[1], app.root.get_primary_color()[2], 0.1 if root.is_playing else 0
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [dp(10), dp(10), dp(10), dp(10)]

    # Animated equalizer (visible only when is_playing is True)
    MDBoxLayout:
        id: equalizer_container
        orientation: 'horizontal'
        size_hint: None, None
        size: dp(45), dp(45)  # Larger size to cover the image
        pos_hint: {"center_y": 0.5}
        opacity: 1 if root.is_playing else 0
        disabled: not root.is_playing
        pos: [root.x + dp(16), root.y + root.height/2 - self.height/2]  # Position over the image

        # Semi-transparent background for equalizer
        canvas.before:
            Color:
                rgba: app.root.get_primary_color()[0], app.root.get_primary_color()[1], app.root.get_primary_color()[2], 0.7  # Semi-transparent primary color
            RoundedRectangle:
                pos: self.pos
                size: self.size
                radius: [dp(22.5), dp(22.5), dp(22.5), dp(22.5)]  # Make it circular

        # Equalizer bars (drawn dynamically in Python)

<SearchScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: 0
        spacing: 0

        # Top app bar
        MDTopAppBar:
            title: "Search Online"
            left_action_items: [["arrow-left", lambda x: app.root.show_main_screen()]]
            right_action_items: [["youtube", lambda x: app.root.open_youtube()]]
            elevation: dp(6)
            md_bg_color: app.root.get_primary_color()
            specific_text_color: app.root.get_text_color()

        # Search box
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_y: None
            height: dp(90)
            padding: [dp(16), dp(12), dp(16), dp(16)]  # left, top, right, bottom
            md_bg_color: app.root.get_primary_color()

            # Search field with integrated button
            MDCard:
                orientation: 'horizontal'
                size_hint_y: None
                height: dp(56)
                spacing: dp(8)
                padding: [dp(4), 0, dp(4), 0]
                md_bg_color: app.root.get_bg_color()
                radius: [dp(28),]  # Rounded corners
                elevation: 3
                ripple_behavior: True

                # Search icon
                MDIconButton:
                    icon: "magnify"
                    theme_text_color: "Custom"
                    text_color: app.root.get_primary_color()
                    user_font_size: sp(24)
                    pos_hint: {"center_y": .5}
                    padding: [dp(12), 0, 0, 0]

                # Search field
                MDTextField:
                    id: search_field
                    hint_text: "Search for songs online..."
                    helper_text: "Enter song name, artist, or album"
                    helper_text_mode: "on_focus"
                    size_hint_x: 0.7
                    pos_hint: {'center_y': 0.5}
                    on_text_validate: root.search_online(self.text)
                    line_color_normal: [0, 0, 0, 0]  # Transparent line
                    line_color_focus: [0, 0, 0, 0]   # Transparent line
                    text_color: app.root.get_text_color()
                    hint_text_color: [0.7, 0.7, 0.7, 1]  # Light gray for hint text
                    font_name: app.root.current_font
                    font_size: sp(16)
                    background_color: [0, 0, 0, 0]   # Transparent background
                    halign: "auto"  # Auto-detect text direction

                # Search button
                MDIconButton:
                    icon: "arrow-right-circle-outline"
                    theme_text_color: "Custom"
                    text_color: app.root.get_primary_color()
                    user_font_size: sp(28)
                    pos_hint: {"center_y": .5}
                    on_release: root.search_online(search_field.text)
                    padding: [0, 0, dp(8), 0]

        # Main content area (with results and bottom bar)
        BoxLayout:
            orientation: 'vertical'

            # Results area
            MDBoxLayout:
                orientation: 'vertical'
                padding: dp(10)
                md_bg_color: app.root.get_bg_color()

                # Loading spinner and message
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(120) if root.is_searching else 0
                    opacity: 1 if root.is_searching else 0
                    elevation: 2
                    radius: [dp(15)]
                    padding: dp(20)
                    md_bg_color: app.root.get_bg_color()
                    pos_hint: {'center_x': .5}
                    disabled: not root.is_searching

                    MDSpinner:
                        size_hint: None, None
                        size: dp(50), dp(50)
                        pos_hint: {'center_x': .5}
                        active: root.is_searching
                        color: app.root.get_primary_color()
                        line_width: dp(3)

                    MDLabel:
                        text: "Searching for music..."
                        halign: "center"
                        theme_text_color: "Secondary"
                        font_style: "Body1"
                        font_name: app.root.current_font
                        size_hint_y: None
                        height: dp(30)
                        pos_hint: {'center_x': .5}

                # Results list
                ScrollView:
                    do_scroll_x: False
                    effect_cls: "ScrollEffect"  # Smoother scrolling
                    bar_width: dp(4)
                    bar_color: app.root.get_primary_color()
                    bar_inactive_color: [0.7, 0.7, 0.7, 0.5]

                    MDList:
                        id: results_list
                        padding: [dp(10), dp(10), dp(10), dp(80)]  # Extra bottom padding for bottom bar
                        spacing: dp(8)
                        md_bg_color: app.root.get_bg_color()

            # Bottom bar (same as in main screen)
            MDCard:
                id: bottom_bar
                orientation: 'vertical'
                size_hint_y: None
                height: dp(80)  # Set to match the height in Python code
                padding: dp(8)  # Small padding
                spacing: dp(5)  # Small spacing
                elevation: dp(12)
                radius: [dp(24), dp(24), 0, 0]
                md_bg_color: app.root.get_primary_color()
                shadow_offset: [0, -dp(2)]
                shadow_softness: dp(12)
                shadow_color: [0, 0, 0, 0.3]
                # Add touch events for swipe up gesture and click to open Now Playing
                on_touch_down: app.root.bottom_bar_touch_down(*args)
                on_touch_up: app.root.bottom_bar_touch_up(*args)
                on_release: app.root.show_now_playing_screen() if app.root.is_playing else None

                # Main content layout
                BoxLayout:
                    orientation: 'horizontal'
                    spacing: dp(8)

                    # Album cover (left side)
                    AsyncImage:
                        id: bottom_bar_album_cover
                        source: app.root.ids.bottom_bar_album_cover.source if hasattr(app.root.ids, 'bottom_bar_album_cover') else ""
                        size_hint: None, None
                        size: dp(50), dp(50)
                        radius: [dp(25),]
                        pos_hint: {'center_y': 0.5}

                    # Song info (center)
                    BoxLayout:
                        orientation: 'vertical'
                        padding: [0, dp(4), 0, dp(4)]
                        size_hint_x: 0.6
                        pos_hint: {'center_y': 0.5}

                        # Song name with scrolling effect
                        MDLabel:
                            id: current_track_name
                            text: app.root.ids.current_track_name.text if hasattr(app.root.ids, 'current_track_name') else ""
                            font_style: "Body1"
                            font_size: sp(14)
                            halign: "left"
                            valign: "middle"
                            size_hint_y: None
                            height: self.texture_size[1]
                            shorten: True
                            shorten_from: "right"
                            markup: True
                            font_name: app.root.current_font

                        # Time info
                        BoxLayout:
                            orientation: 'horizontal'
                            size_hint_y: None
                            height: dp(20)
                            spacing: dp(4)

                            # Current time
                            MDLabel:
                                id: current_time_main
                                text: app.root.ids.current_time_main.text if hasattr(app.root.ids, 'current_time_main') else "0:00"
                                font_style: "Caption"
                                font_size: sp(12)
                                size_hint_x: None
                                width: dp(40)
                                halign: "left"
                                valign: "middle"
                                theme_text_color: "Secondary"

                            # Progress bar
                            CircularProgressBar:
                                id: play_progress_main
                                size_hint_x: 1
                                height: dp(2)
                                max: 100
                                value: app.root.ids.play_progress_main.value if hasattr(app.root.ids, 'play_progress_main') else 0
                                cap_style: "round"
                                cap_precision: 100
                                progress_color: app.root.get_primary_color()
                                background_color: [0.7, 0.7, 0.7, 0.3]

                            # Total time
                            MDLabel:
                                id: total_time_main
                                text: app.root.ids.total_time_main.text if hasattr(app.root.ids, 'total_time_main') else "0:00"
                                font_style: "Caption"
                                font_size: sp(12)
                                size_hint_x: None
                                width: dp(40)
                                halign: "right"
                                valign: "middle"
                                theme_text_color: "Secondary"

                    # Controls (right side)
                    BoxLayout:
                        orientation: 'horizontal'
                        size_hint_x: 0.2
                        spacing: dp(4)
                        padding: [0, 0, dp(4), 0]  # Add right padding
                        pos_hint: {'center_y': 0.5, 'right': 1}

                        # Play/Pause button
                        MDIconButton:
                            icon: 'play' if not app.root.is_playing else 'pause'
                            theme_text_color: "Custom"
                            text_color: app.root.get_text_color()
                            on_release: app.root.toggle_play_only()
                            md_bg_color: [1, 1, 1, 0.1]
                            size_hint: None, None
                            size: dp(44), dp(44)
                            radius: [dp(22),]
                            pos_hint: {'center_x': 0.5, 'center_y': 0.5}
