#!/usr/bin/env python3
"""
إصلاح مشكلة تخطيط الواجهة في مشغل الموسيقى العربي
Fix UI layout issues in Arabic Music Player
"""

import os
import shutil
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def backup_original_files():
    """إنشاء نسخ احتياطية من الملفات الأصلية"""
    logger.info("🔄 Creating backup of original files...")
    
    files_to_backup = ['main.py', 'MusicPlayer.kv']
    
    for file_name in files_to_backup:
        if os.path.exists(file_name):
            backup_name = f"{file_name}.backup"
            try:
                shutil.copy(file_name, backup_name)
                logger.info(f"✅ Created backup: {backup_name}")
            except Exception as e:
                logger.error(f"❌ Failed to backup {file_name}: {e}")

def fix_kv_layout():
    """إصلاح مشاكل التخطيط في ملف KV"""
    logger.info("🔧 Fixing KV layout issues...")
    
    # التحقق من وجود الإصلاح في MusicPlayer.kv
    if os.path.exists('MusicPlayer.kv'):
        try:
            with open('MusicPlayer.kv', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص المشاكل الشائعة
            issues_found = []
            
            if 'size_hint: 1, 2' in content:
                issues_found.append("size_hint: 1, 2 should be size_hint: 1, 1")
                content = content.replace('size_hint: 1, 2', 'size_hint: 1, 1')
            
            if 'size_hint_y: None' in content and 'height: dp(' not in content:
                issues_found.append("Missing height specification for size_hint_y: None")
            
            # حفظ الملف المحدث
            if issues_found:
                with open('MusicPlayer.kv', 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info("✅ Fixed KV layout issues:")
                for issue in issues_found:
                    logger.info(f"   - {issue}")
            else:
                logger.info("✅ No layout issues found in MusicPlayer.kv")
                
        except Exception as e:
            logger.error(f"❌ Error fixing KV file: {e}")
    else:
        logger.warning("❌ MusicPlayer.kv not found")

def test_with_simple_ui():
    """اختبار مع واجهة مبسطة"""
    logger.info("🧪 Setting up simple UI test...")
    
    # نسخ الملفات المبسطة للاختبار
    if os.path.exists('main_ui_test.py') and os.path.exists('MusicPlayer_simple_test.kv'):
        try:
            # نسخ احتياطية من main.py الحالي
            if os.path.exists('main.py'):
                shutil.copy('main.py', 'main_complex.py')
                logger.info("✅ Saved complex main.py as main_complex.py")
            
            # استخدام النسخة المبسطة
            shutil.copy('main_ui_test.py', 'main.py')
            logger.info("✅ Using simplified main.py for testing")
            
            # نسخ احتياطية من MusicPlayer.kv الحالي
            if os.path.exists('MusicPlayer.kv'):
                shutil.copy('MusicPlayer.kv', 'MusicPlayer_complex.kv')
                logger.info("✅ Saved complex MusicPlayer.kv as MusicPlayer_complex.kv")
            
            # استخدام النسخة المبسطة
            shutil.copy('MusicPlayer_simple_test.kv', 'MusicPlayer.kv')
            logger.info("✅ Using simplified MusicPlayer.kv for testing")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up simple UI test: {e}")
            return False
    else:
        logger.error("❌ Simple UI test files not found")
        return False

def restore_complex_ui():
    """استعادة الواجهة المعقدة"""
    logger.info("🔄 Restoring complex UI...")
    
    try:
        if os.path.exists('main_complex.py'):
            shutil.copy('main_complex.py', 'main.py')
            logger.info("✅ Restored complex main.py")
        
        if os.path.exists('MusicPlayer_complex.kv'):
            shutil.copy('MusicPlayer_complex.kv', 'MusicPlayer.kv')
            logger.info("✅ Restored complex MusicPlayer.kv")
            
        return True
    except Exception as e:
        logger.error(f"❌ Error restoring complex UI: {e}")
        return False

def diagnose_ui_issues():
    """تشخيص مشاكل الواجهة"""
    logger.info("🔍 Diagnosing UI issues...")
    
    issues = []
    
    # فحص ملف KV
    if os.path.exists('MusicPlayer.kv'):
        try:
            with open('MusicPlayer.kv', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص المشاكل الشائعة
            if 'size_hint: 1, 2' in content:
                issues.append("❌ Invalid size_hint: 1, 2 found (should be 1, 1)")
            
            if content.count('size_hint: 1, 1') < 3:
                issues.append("⚠️ Missing size_hint: 1, 1 in some layouts")
            
            if 'MDNavigationLayout' not in content:
                issues.append("⚠️ MDNavigationLayout not found")
            
            if 'ScreenManager' not in content:
                issues.append("⚠️ ScreenManager not found")
                
        except Exception as e:
            issues.append(f"❌ Error reading MusicPlayer.kv: {e}")
    else:
        issues.append("❌ MusicPlayer.kv not found")
    
    # فحص ملف Python
    if os.path.exists('main.py'):
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'class MusicPlayer(BoxLayout)' not in content:
                issues.append("❌ MusicPlayer class not found or not inheriting from BoxLayout")
            
            if 'Builder.load_file' not in content:
                issues.append("⚠️ KV file loading not found")
                
        except Exception as e:
            issues.append(f"❌ Error reading main.py: {e}")
    else:
        issues.append("❌ main.py not found")
    
    # طباعة النتائج
    if issues:
        logger.info("🚨 UI Issues found:")
        for issue in issues:
            logger.info(f"   {issue}")
    else:
        logger.info("✅ No obvious UI issues found")
    
    return issues

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎯 Arabic Music Player - UI Layout Fix")
    print("=" * 60)
    
    logger.info("🚀 Starting UI layout fix process...")
    
    # الخطوة 1: إنشاء نسخ احتياطية
    print("\n📋 Step 1: Creating backups...")
    backup_original_files()
    
    # الخطوة 2: تشخيص المشاكل
    print("\n📋 Step 2: Diagnosing UI issues...")
    issues = diagnose_ui_issues()
    
    # الخطوة 3: إصلاح ملف KV
    print("\n📋 Step 3: Fixing KV layout...")
    fix_kv_layout()
    
    # الخطوة 4: اختبار مع واجهة مبسطة
    print("\n📋 Step 4: Testing with simple UI...")
    simple_test_success = test_with_simple_ui()
    
    if simple_test_success:
        print("\n🎉 Simple UI test setup completed!")
        print("📱 Now build and test the APK:")
        print("   buildozer android debug")
        print("\n💡 If simple UI works:")
        print("   1. The issue is in the complex layout")
        print("   2. Gradually add features back")
        print("   3. Use: python fix_ui_layout.py --restore")
        print("\n💡 If simple UI doesn't work:")
        print("   1. The issue is more fundamental")
        print("   2. Check Android logs: adb logcat | grep python")
        print("   3. Verify KivyMD installation")
    else:
        print("\n❌ Failed to setup simple UI test")
        print("💡 Try manually copying files:")
        print("   cp main_ui_test.py main.py")
        print("   cp MusicPlayer_simple_test.kv MusicPlayer.kv")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--restore':
        print("🔄 Restoring complex UI...")
        restore_complex_ui()
        print("✅ Complex UI restored")
    else:
        main()
