"""
شريط تقدم مخصص يعمل بشكل صحيح مع مشغل الموسيقى.
يعرض الوقت الحالي فوق نقطة التقدم.
"""

from kivy.uix.slider import Slider
from kivy.properties import BooleanProperty, NumericProperty, StringProperty
from kivy.graphics import Color, Rectangle
from kivy.clock import Clock
from kivy.uix.label import Label
from kivy.metrics import dp

class TimeLabel(Label):
    """تسمية تعرض الوقت فوق نقطة التقدم"""
    def __init__(self, **kwargs):
        # Usar super() correctamente
        super(TimeLabel, self).__init__(**kwargs)
        self.size_hint = (None, None)
        self.height = dp(20)
        self.width = dp(50)
        self.font_size = dp(12)
        self.halign = 'center'
        self.valign = 'middle'
        self.color = [1, 1, 1, 1]
        self.padding = [dp(5), dp(2)]

        # إضافة خلفية لتحسين الرؤية
        with self.canvas.before:
            Color(0.2, 0.2, 0.2, 0.7)  # خلفية داكنة شبه شفافة
            self.bg_rect = Rectangle(pos=self.pos, size=self.size)

        self.bind(pos=self._update_rect, size=self._update_rect)

    def _update_rect(self, *args):
        """تحديث موضع وحجم الخلفية"""
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class CustomSlider(Slider):
    """شريط تقدم مخصص يعمل بشكل صحيح مع مشغل الموسيقى ويعرض الوقت فوق نقطة التقدم"""

    is_seeking = BooleanProperty(False)
    time_text = StringProperty("00:00")
    show_time_label = BooleanProperty(True)

    def __init__(self, **kwargs):
        # Usar super() correctamente
        super(CustomSlider, self).__init__(**kwargs)
        self.min = 0
        self.max = 100
        self.value = 0
        self.step = 0.1
        self.orientation = 'horizontal'
        self.cursor_size = (20, 20)

        # إنشاء تسمية الوقت
        self.time_label = TimeLabel(text=self.time_text)

        # إضافة رسم مخصص
        with self.canvas.before:
            Color(0.2, 0.2, 0.2, 1)  # لون الخلفية
            self.background = Rectangle(pos=self.pos, size=self.size)
            Color(0.2, 0.7, 1, 1)  # لون التقدم
            self.progress = Rectangle(pos=self.pos, size=(0, self.height))

        # ربط التغييرات في الخصائص
        self.bind(pos=self.update_graphics_pos)
        self.bind(size=self.update_graphics_size)
        self.bind(value=self.update_graphics_pos, max=self.update_graphics_pos)
        self.bind(time_text=self._update_time_label)

        # جدولة إضافة تسمية الوقت بعد التهيئة
        Clock.schedule_once(self._add_time_label, 0)

    def _add_time_label(self, dt):
        """إضافة تسمية الوقت إلى الأب بعد التهيئة"""
        if self.parent:
            self.parent.add_widget(self.time_label)
            self._update_time_label_pos()
        else:
            # المحاولة مرة أخرى لاحقًا إذا لم يكن الأب متاحًا بعد
            Clock.schedule_once(self._add_time_label, 0.1)

    def _update_time_label(self, instance, value):
        """تحديث نص تسمية الوقت"""
        self.time_label.text = value
        self._update_time_label_pos()

    def _update_time_label_pos(self, *args):
        """تحديث موضع تسمية الوقت لتتبع نقطة التقدم"""
        if not hasattr(self, 'time_label') or not self.parent:
            return

        # حساب موضع نقطة التقدم
        if self.max > self.min:
            ratio = (self.value - self.min) / (self.max - self.min)
        else:
            ratio = 0

        # حساب موضع x (مع مراعاة الهوامش)
        x_pos = self.x + (ratio * self.width)

        # توسيط التسمية فوق نقطة التقدم
        self.time_label.x = x_pos - (self.time_label.width / 2)
        self.time_label.y = self.y + self.height + dp(5)  # وضعها فوق الشريط

    def update_graphics_pos(self, instance, value):
        """تحديث موضع الرسومات"""
        self.background.pos = self.pos
        self.progress.pos = self.pos

        # حساب عرض التقدم
        if self.max > 0:
            progress_width = (self.value / self.max) * self.width
        else:
            progress_width = 0

        # تحديث حجم التقدم
        self.progress.size = (progress_width, self.height)

        # تحديث موضع تسمية الوقت
        self._update_time_label_pos()

    def update_graphics_size(self, instance, value):
        """تحديث حجم الرسومات"""
        self.background.size = self.size

        # حساب عرض التقدم
        if self.max > 0:
            progress_width = (self.value / self.max) * self.width
        else:
            progress_width = 0

        # تحديث حجم التقدم
        self.progress.size = (progress_width, self.height)

        # تحديث موضع تسمية الوقت
        self._update_time_label_pos()

    def on_touch_down(self, touch):
        """التعامل مع الضغط على الشريط"""
        if self.collide_point(*touch.pos):
            # تعيين علامة البحث
            self.is_seeking = True

            # حساب القيمة الجديدة
            value = self.min + (touch.x - self.x) / self.width * (self.max - self.min)
            self.value = value

            # تحديث موضع تسمية الوقت
            self._update_time_label_pos()

            # إعلام الأب
            if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'on_slider_down'):
                self.parent.parent.on_slider_down()

            return True
        return super(CustomSlider, self).on_touch_down(touch)

    def on_touch_move(self, touch):
        """التعامل مع السحب على الشريط"""
        if self.is_seeking and self.collide_point(*touch.pos):
            # حساب القيمة الجديدة
            value = self.min + (touch.x - self.x) / self.width * (self.max - self.min)
            self.value = value

            # تحديث موضع تسمية الوقت
            self._update_time_label_pos()

            return True
        return super(CustomSlider, self).on_touch_move(touch)

    def on_touch_up(self, touch):
        """التعامل مع رفع الإصبع عن الشريط"""
        if self.is_seeking and self.collide_point(*touch.pos):
            # حساب القيمة الجديدة
            value = self.min + (touch.x - self.x) / self.width * (self.max - self.min)
            self.value = value

            # تحديث موضع تسمية الوقت
            self._update_time_label_pos()

            # إعلام الأب
            if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'on_slider_up'):
                self.parent.parent.on_slider_up(value)

            # إعادة تعيين علامة البحث
            self.is_seeking = False
            return True

        # إعادة تعيين علامة البحث في حالة رفع الإصبع خارج الشريط
        self.is_seeking = False
        return super(CustomSlider, self).on_touch_up(touch)

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
