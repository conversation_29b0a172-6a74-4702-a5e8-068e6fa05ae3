<Screen name="audio_settings">
    BoxLayout:
        orientation: 'vertical'
        MDTopAppBar:
            title: "Audio Settings"
            left_action_items: [["arrow-left", lambda x: app.root.show_main_screen()]]
            elevation: 10
            
        ScrollView:
            BoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: dp(16)
                spacing: dp(16)
                
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Audio Enhancement"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    BoxLayout:
                        size_hint_y: None
                        height: dp(48)
                        
                        MDLabel:
                            text: "Enable Audio Enhancement"
                            
                        MDSwitch:
                            id: audio_enhancement_switch
                            active: app.root.audio_enhancement_enabled
                            on_active: app.root.toggle_audio_enhancement(self.active)
                            
                    BoxLayout:
                        size_hint_y: None
                        height: dp(48)
                        
                        MDLabel:
                            text: "Volume Normalization"
                            
                        MDSwitch:
                            id: volume_normalization_switch
                            active: app.root.volume_normalization
                            on_active: app.root.toggle_volume_normalization(self.active)
                
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Equalizer"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        BoxLayout:
                            size_hint_y: None
                            height: dp(20)
                            
                            MDLabel:
                                text: "Bass"
                                size_hint_x: 0.3
                                
                            MDLabel:
                                id: bass_value_label
                                text: str(int(bass_slider.value)) + " dB"
                                halign: 'right'
                                size_hint_x: 0.2
                                
                        MDSlider:
                            id: bass_slider
                            min: -10
                            max: 10
                            step: 1
                            value: app.root.bass_level
                            on_value: app.root.set_bass_level(self.value)
                            
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        BoxLayout:
                            size_hint_y: None
                            height: dp(20)
                            
                            MDLabel:
                                text: "Mid"
                                size_hint_x: 0.3
                                
                            MDLabel:
                                id: mid_value_label
                                text: str(int(mid_slider.value)) + " dB"
                                halign: 'right'
                                size_hint_x: 0.2
                                
                        MDSlider:
                            id: mid_slider
                            min: -10
                            max: 10
                            step: 1
                            value: app.root.mid_level
                            on_value: app.root.set_mid_level(self.value)
                            
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        BoxLayout:
                            size_hint_y: None
                            height: dp(20)
                            
                            MDLabel:
                                text: "Treble"
                                size_hint_x: 0.3
                                
                            MDLabel:
                                id: treble_value_label
                                text: str(int(treble_slider.value)) + " dB"
                                halign: 'right'
                                size_hint_x: 0.2
                                
                        MDSlider:
                            id: treble_slider
                            min: -10
                            max: 10
                            step: 1
                            value: app.root.treble_level
                            on_value: app.root.set_treble_level(self.value)
                            
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Audio Presets"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    GridLayout:
                        cols: 2
                        spacing: dp(8)
                        size_hint_y: None
                        height: self.minimum_height
                        
                        MDRaisedButton:
                            text: "Normal"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("normal")
                            
                        MDRaisedButton:
                            text: "Bass Boost"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("bass_boost")
                            
                        MDRaisedButton:
                            text: "Treble Boost"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("treble_boost")
                            
                        MDRaisedButton:
                            text: "Vocal Boost"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("vocal_boost")
                            
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Buffer Size"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        MDLabel:
                            text: "Larger buffer size improves audio quality but increases latency"
                            size_hint_y: None
                            height: self.texture_size[1]
                            font_style: 'Caption'
                            
                        MDDropDownItem:
                            id: buffer_size_dropdown
                            text: "8192 bytes"
                            on_release: app.root.show_buffer_size_menu()
