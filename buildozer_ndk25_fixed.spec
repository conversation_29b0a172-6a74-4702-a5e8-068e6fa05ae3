[app]

# (str) Title of your application
title = ArabicPlayer

# (str) Package name
package.name = arabicplayer

# (str) Package domain (needed for android/ios packaging)
package.domain = org.arabicplayer

# (str) Source code where the main.py live
source.dir = .

# (list) Source files to include (let empty to include all the files)
source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json,txt

# (list) List of inclusions using pattern matching
source.include_patterns = assets/*,images/*.png,images/*.jpg,fonts/*.ttf,default_covers/*.jpg,*.py,*.kv,*.json

# (list) Source files to exclude (let empty to not exclude anything)
source.exclude_exts = spec

# (list) List of directory to exclude (let empty to not exclude anything)
source.exclude_dirs = tests,bin,.buildozer,venv,__pycache__,downloads,music,temp_covers,converted_audio,album_art

# (list) List of exclusions using pattern matching
source.exclude_patterns = license,images/*/*.jpg

# (str) Application versioning (method 1)
version = 1.0

# (list) Application requirements
# comma separated e.g. requirements = sqlite3,kivy
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,mutagen==1.47.0,pyjnius,android,python-bidi==0.4.2,arabic-reshaper==3.0.0,pillow==10.0.1,requests==2.31.0,plyer==2.1.0,certifi

# (str) Presplash of the application
presplash.filename = %(source.dir)s/images/default_album_cover.png

# (str) Icon of the application
icon.filename = %(source.dir)s/images/default_album_cover.png

# (str) Supported orientation (landscape, sensorLandscape, portrait or all)
orientation = portrait

# (bool) Indicate if the application should be fullscreen or not
fullscreen = 0

# (list) Permissions
android.permissions = INTERNET,WRITE_EXTERNAL_STORAGE,READ_EXTERNAL_STORAGE,MANAGE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS,RECORD_AUDIO

# (str) Android logcat filters to use
android.logcat_filters = *:S python:D

# (str) Android entry point, default is ok for Kivy-based app
android.entrypoint = org.kivy.android.PythonActivity

# (list) Gradle repositories to add {can be necessary for some android.gradle_dependencies}
# please enclose in double quotes 
android.gradle_repositories = "google()", "mavenCentral()", "maven { url 'https://jitpack.io' }"

# (list) Gradle dependencies to add
android.gradle_dependencies = com.google.android.material:material:1.9.0, androidx.core:core:1.10.1, androidx.appcompat:appcompat:1.6.1, androidx.constraintlayout:constraintlayout:2.1.4

# (bool) Enable AndroidX support. Enable when 'android.gradle_dependencies'
# contains an 'androidx' package, or any package from Kotlin source.
# android.enable_androidx requires android.api >= 28
android.enable_androidx = True

# (str) Android SDK version to use
android.api = 34

# (str) Android NDK version to use - FIXED TO NDK 25
android.ndk = 25b

# (str) Android NDK API to use. This is the minimum API your app will support, it should usually match android.minapi.
android.ndk_api = 21

# (int) Android API to use (targetSdkVersion AND compileSdkVersion)
android.sdk = 34

# (bool) Whether or not to copy the Android SDK/NDK to the buildozer directory
android.copy_libs = 1

# (list) The Android archs to build for, choices: armeabi-v7a, arm64-v8a, x86, x86_64
android.archs = arm64-v8a

# (bool) enables Android auto backup feature (Android API >=23)
android.allow_backup = True

# (str) Use 'android.accept_sdk_license = yes' to automatically accept the license
android.accept_sdk_license = True

[buildozer]

# (int) Log level (0 = error only, 1 = info, 2 = debug (with command output))
log_level = 2

# (int) Display warning if buildozer is run as root (0 = False, 1 = True)
warn_on_root = 0
