"""
نسخة أبسط من التطبيق للاختبار على Colab
تحتوي على الحد الأدنى المطلق من الكود
"""

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button

class MinimalTestWidget(BoxLayout):
    """واجهة بسيطة جداً للاختبار"""
    
    def __init__(self, **kwargs):
        super(MinimalTestWidget, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # تسمية ترحيب
        welcome_label = Label(
            text='Arabic Music Player Test\nمشغل الموسيقى العربي',
            font_size='18sp',
            halign='center'
        )
        welcome_label.bind(size=welcome_label.setter('text_size'))
        self.add_widget(welcome_label)
        
        # تسمية حالة
        self.status_label = Label(
            text='App is working!\nالتطبيق يعمل!',
            font_size='14sp',
            halign='center'
        )
        self.status_label.bind(size=self.status_label.setter('text_size'))
        self.add_widget(self.status_label)
        
        # زر اختبار
        test_button = Button(
            text='Test Button / زر الاختبار',
            size_hint=(1, None),
            height='50dp'
        )
        test_button.bind(on_press=self.on_test_button)
        self.add_widget(test_button)
        
    def on_test_button(self, instance):
        """معالج زر الاختبار"""
        self.status_label.text = 'Button pressed!\nتم الضغط على الزر!'

class MinimalTestApp(App):
    """تطبيق بسيط للاختبار"""
    
    def build(self):
        return MinimalTestWidget()

if __name__ == '__main__':
    MinimalTestApp().run()
