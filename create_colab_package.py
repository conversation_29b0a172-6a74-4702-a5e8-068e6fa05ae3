#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لضغط الملفات المطلوبة فقط لرفعها على Google Colab
"""

import os
import zipfile
import shutil
from pathlib import Path

def create_colab_package():
    """إنشاء حزمة مضغوطة تحتوي على الملفات المطلوبة فقط"""

    # اسم الملف المضغوط
    zip_filename = "ArabicPlayer_Android_Only_v2.zip"

    # الملفات المطلوبة
    required_files = [
        # الملفات الأساسية
        "main.py",
        "buildozer.spec",
        ".buildozerignore",

        # ملفات Python
        "search_screen.py",
        "download_screen.py",
        "download_manager.py",
        "custom_slider.py",
        "playing_indicator.py",
        "audio_enhancer.py",
        "arabic_utils.py",
        "color_circle.py",
        "theme_manager.py",
        "themes.py",
        "performance_optimizer.py",
        "create_default_cover.py",
        "improved_song_item.py",
        "test_search_screen.py",

        # ملفات KV
        "MusicPlayer.kv",
        "search_screen.kv",
        "download_screen.kv",
        "custom_slider.kv",
        "improved_song_item.kv",
        "audio_settings.kv",
        "improved_main_screen.kv",

        # ملفات JSON (اختيارية)
        "favorites.json",
        "theme.json",
        "enhanced_theme.json",
        "playlist.json",

        # ملفات التوثيق الجديدة
        "ANDROID_ONLY_VERSION.md",
        "FINAL_SYNTAX_FIX.md",
        "COLAB_TROUBLESHOOTING_GUIDE.md",
        "README_FINAL.md",
    ]

    # المجلدات المطلوبة
    required_dirs = [
        "fonts",
        "images",
        "default_covers"
    ]

    print("🚀 بدء إنشاء حزمة الأندرويد المحسنة...")

    # حذف الملف المضغوط إذا كان موجوداً
    if os.path.exists(zip_filename):
        os.remove(zip_filename)
        print(f"🗑️  تم حذف الملف القديم: {zip_filename}")

    # إنشاء الملف المضغوط
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:

        # إضافة الملفات المطلوبة
        files_added = 0
        files_missing = 0

        for file_path in required_files:
            if os.path.exists(file_path):
                zipf.write(file_path, file_path)
                print(f"✅ تم إضافة: {file_path}")
                files_added += 1
            else:
                print(f"⚠️  ملف مفقود: {file_path}")
                files_missing += 1

        # إضافة المجلدات المطلوبة
        dirs_added = 0
        dirs_missing = 0

        for dir_path in required_dirs:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                for root, dirs, files in os.walk(dir_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = file_path.replace('\\', '/')  # تصحيح المسارات لـ Windows
                        zipf.write(file_path, arcname)
                        print(f"✅ تم إضافة: {arcname}")
                        files_added += 1
                dirs_added += 1
            else:
                print(f"⚠️  مجلد مفقود: {dir_path}")
                dirs_missing += 1

    # عرض الإحصائيات
    print("\n" + "="*50)
    print("📊 إحصائيات الضغط:")
    print(f"✅ ملفات مضافة: {files_added}")
    print(f"⚠️  ملفات مفقودة: {files_missing}")
    print(f"✅ مجلدات مضافة: {dirs_added}")
    print(f"⚠️  مجلدات مفقودة: {dirs_missing}")

    # حجم الملف المضغوط
    if os.path.exists(zip_filename):
        file_size = os.path.getsize(zip_filename)
        file_size_mb = file_size / (1024 * 1024)
        print(f"📦 حجم الملف المضغوط: {file_size_mb:.2f} MB")
        print(f"✅ تم إنشاء الملف بنجاح: {zip_filename}")

        # نصائح للاستخدام
        print("\n" + "="*50)
        print("📋 خطوات الاستخدام:")
        print("1. ارفع الملف إلى Google Drive")
        print("2. في Google Colab، حدث مسار الملف:")
        print(f'   zip_path = "/content/drive/MyDrive/{zip_filename}"')
        print("3. هذه النسخة محسنة للأندرويد فقط - أداء أفضل وحجم أصغر!")
        print("3. شغل خلايا Colab بالترتيب")
        print("4. احصل على APK جاهز!")

        return True
    else:
        print("❌ فشل في إنشاء الملف المضغوط!")
        return False

def verify_package():
    """التحقق من محتويات الحزمة المضغوطة"""
    zip_filename = "ArabicPlayer_Android_Only_v2.zip"

    if not os.path.exists(zip_filename):
        print(f"❌ الملف غير موجود: {zip_filename}")
        return False

    print(f"\n🔍 فحص محتويات: {zip_filename}")
    print("-" * 40)

    with zipfile.ZipFile(zip_filename, 'r') as zipf:
        file_list = zipf.namelist()

        # تصنيف الملفات
        python_files = [f for f in file_list if f.endswith('.py')]
        kv_files = [f for f in file_list if f.endswith('.kv')]
        json_files = [f for f in file_list if f.endswith('.json')]
        font_files = [f for f in file_list if 'fonts/' in f]
        image_files = [f for f in file_list if 'images/' in f or 'default_covers/' in f]
        other_files = [f for f in file_list if not any(f.endswith(ext) for ext in ['.py', '.kv', '.json']) and not any(folder in f for folder in ['fonts/', 'images/', 'default_covers/'])]

        print(f"🐍 ملفات Python: {len(python_files)}")
        for f in python_files[:5]:  # عرض أول 5 ملفات
            print(f"   - {f}")
        if len(python_files) > 5:
            print(f"   ... و {len(python_files) - 5} ملف آخر")

        print(f"\n🎨 ملفات KV: {len(kv_files)}")
        for f in kv_files:
            print(f"   - {f}")

        print(f"\n📄 ملفات JSON: {len(json_files)}")
        for f in json_files:
            print(f"   - {f}")

        print(f"\n🔤 ملفات الخطوط: {len(font_files)}")
        for f in font_files:
            print(f"   - {f}")

        print(f"\n🖼️  ملفات الصور: {len(image_files)}")
        for f in image_files[:3]:  # عرض أول 3 ملفات
            print(f"   - {f}")
        if len(image_files) > 3:
            print(f"   ... و {len(image_files) - 3} ملف آخر")

        print(f"\n📋 ملفات أخرى: {len(other_files)}")
        for f in other_files:
            print(f"   - {f}")

        print(f"\n📊 إجمالي الملفات: {len(file_list)}")

    return True

if __name__ == "__main__":
    print("🎵 مرحباً بك في أداة تحضير حزمة الأندرويد المحسنة")
    print("=" * 50)

    # إنشاء الحزمة
    success = create_colab_package()

    if success:
        # التحقق من الحزمة
        verify_package()

        print("\n" + "="*50)
        print("🎉 تم الانتهاء بنجاح!")
        print("📦 الملف جاهز للرفع على Google Colab")
    else:
        print("\n❌ فشل في إنشاء الحزمة!")
