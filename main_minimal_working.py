#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل الموسيقى العربي - إصدار مبسط يعمل بدون أخطاء
Arabic Music Player - Minimal Working Version
"""

import sys
import os
import traceback
import logging

# إعداد نظام السجلات البسيط
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def crash_handler(exc_type, exc_value, exc_traceback):
    """معالج شامل لتحطم التطبيق"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    crash_info = f"""
=== تحطم التطبيق ===
النوع: {exc_type.__name__}
الرسالة: {str(exc_value)}
التفاصيل:
{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}
===================
"""
    
    logger.error(crash_info)
    print(crash_info)

# تعيين معالج التحطم
sys.excepthook = crash_handler

logger.info("🚀 بدء تشغيل مشغل الموسيقى العربي المبسط...")

# استيراد آمن للمكتبات
def safe_import(module_name, package=None):
    """استيراد آمن للمكتبات"""
    try:
        if package:
            module = __import__(module_name, fromlist=[package])
            return getattr(module, package)
        else:
            return __import__(module_name)
    except ImportError as e:
        logger.error(f"❌ فشل في استيراد {module_name}: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ خطأ في استيراد {module_name}: {e}")
        return None

# استيراد Kivy
logger.info("📦 استيراد Kivy...")
kivy = safe_import('kivy')
if not kivy:
    logger.error("❌ Kivy غير متاح")
    sys.exit(1)

# إعداد Kivy
try:
    from kivy.config import Config
    Config.set('graphics', 'resizable', False)
    Config.set('kivy', 'exit_on_escape', '0')
    logger.info("✅ تم إعداد Kivy")
except Exception as e:
    logger.warning(f"⚠️ تحذير في إعداد Kivy: {e}")

# استيراد مكونات Kivy الأساسية
try:
    from kivy.app import App
    from kivy.uix.boxlayout import BoxLayout
    from kivy.uix.label import Label
    from kivy.uix.button import Button
    from kivy.clock import Clock
    from kivy.uix.progressbar import ProgressBar
    from kivy.uix.slider import Slider
    logger.info(f"✅ Kivy {kivy.__version__} جاهز")
except ImportError as e:
    logger.error(f"❌ خطأ في استيراد مكونات Kivy: {e}")
    sys.exit(1)

# استيراد KivyMD (اختياري)
kivymd = safe_import('kivymd')
USE_KIVYMD = False

if kivymd:
    try:
        from kivymd.app import MDApp
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.label import MDLabel
        from kivymd.uix.button import MDRaisedButton, MDIconButton
        from kivymd.uix.slider import MDSlider
        from kivymd.uix.card import MDCard
        logger.info(f"✅ KivyMD {kivymd.__version__} متاح")
        USE_KIVYMD = True
    except ImportError as e:
        logger.warning(f"⚠️ بعض مكونات KivyMD غير متاحة: {e}")
        USE_KIVYMD = False

# Android permissions (اختياري)
ANDROID_AVAILABLE = False
try:
    from android.permissions import request_permissions, Permission
    logger.info("✅ Android permissions متاحة")
    ANDROID_AVAILABLE = True
except ImportError:
    logger.info("ℹ️ تشغيل على سطح المكتب")

# مكتبات إضافية (اختيارية)
mutagen = safe_import('mutagen')
requests = safe_import('requests')

class MusicPlayerApp(MDApp if USE_KIVYMD else App):
    """التطبيق الرئيسي المبسط"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "مشغل الموسيقى العربي"
        logger.info("🎵 تهيئة التطبيق...")
        
        # متغيرات بسيطة
        self.is_playing = False
        self.current_song = None
        self.progress_value = 0
        self.max_value = 100
        
    def build(self):
        """بناء واجهة التطبيق المبسطة"""
        try:
            logger.info("🔧 بناء واجهة المستخدم...")
            
            # إعداد الثيم
            if USE_KIVYMD and hasattr(self, 'theme_cls'):
                self.theme_cls.theme_style = "Dark"
                self.theme_cls.primary_palette = "Blue"
            
            # التخطيط الرئيسي
            if USE_KIVYMD:
                main_layout = MDBoxLayout(
                    orientation='vertical',
                    padding=20,
                    spacing=15,
                    adaptive_height=True
                )
            else:
                main_layout = BoxLayout(
                    orientation='vertical',
                    padding=20,
                    spacing=15
                )
            
            # عنوان التطبيق
            title_widget = self.create_title()
            main_layout.add_widget(title_widget)
            
            # معلومات النظام
            info_widget = self.create_info_panel()
            main_layout.add_widget(info_widget)
            
            # شريط التقدم
            progress_widget = self.create_progress_bar()
            main_layout.add_widget(progress_widget)
            
            # أزرار التحكم
            controls_widget = self.create_controls()
            main_layout.add_widget(controls_widget)
            
            # بدء تحديث شريط التقدم
            Clock.schedule_interval(self.update_progress, 0.1)
            
            logger.info("✅ تم بناء الواجهة بنجاح")
            return main_layout
            
        except Exception as e:
            logger.error(f"❌ خطأ في بناء الواجهة: {e}")
            return self.create_error_layout(str(e))
    
    def create_title(self):
        """إنشاء عنوان التطبيق"""
        if USE_KIVYMD:
            return MDLabel(
                text="🎵 مشغل الموسيقى العربي",
                halign="center",
                theme_text_color="Primary",
                font_style="H4",
                size_hint_y=None,
                height=80
            )
        else:
            return Label(
                text="🎵 مشغل الموسيقى العربي",
                halign="center",
                size_hint_y=None,
                height=80,
                font_size=24
            )
    
    def create_info_panel(self):
        """إنشاء لوحة المعلومات"""
        info_text = self.get_system_info()
        
        if USE_KIVYMD:
            return MDLabel(
                text=info_text,
                halign="center",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=150
            )
        else:
            return Label(
                text=info_text,
                halign="center",
                size_hint_y=None,
                height=150,
                text_size=(None, None)
            )
    
    def create_progress_bar(self):
        """إنشاء شريط التقدم"""
        if USE_KIVYMD:
            progress_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=80,
                spacing=10
            )
            
            # تسمية شريط التقدم
            progress_label = MDLabel(
                text="شريط التقدم التجريبي",
                halign="center",
                size_hint_y=None,
                height=30
            )
            progress_layout.add_widget(progress_label)
            
            # شريط التقدم
            self.progress_bar = MDSlider(
                min=0,
                max=100,
                value=0,
                size_hint_y=None,
                height=40
            )
            progress_layout.add_widget(self.progress_bar)
            
            return progress_layout
        else:
            progress_layout = BoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=80,
                spacing=10
            )
            
            progress_label = Label(
                text="شريط التقدم التجريبي",
                halign="center",
                size_hint_y=None,
                height=30
            )
            progress_layout.add_widget(progress_label)
            
            self.progress_bar = Slider(
                min=0,
                max=100,
                value=0,
                size_hint_y=None,
                height=40
            )
            progress_layout.add_widget(self.progress_bar)
            
            return progress_layout
    
    def create_controls(self):
        """إنشاء أزرار التحكم"""
        if USE_KIVYMD:
            controls_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=60,
                spacing=20,
                adaptive_width=True,
                pos_hint={'center_x': 0.5}
            )
            
            # زر التشغيل/الإيقاف
            self.play_button = MDRaisedButton(
                text="▶️ تشغيل",
                size_hint=(None, None),
                size=(120, 50)
            )
            self.play_button.bind(on_press=self.toggle_playback)
            controls_layout.add_widget(self.play_button)
            
            # زر الاختبار
            test_button = MDRaisedButton(
                text="🧪 اختبار",
                size_hint=(None, None),
                size=(120, 50)
            )
            test_button.bind(on_press=self.test_function)
            controls_layout.add_widget(test_button)
            
        else:
            controls_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=60,
                spacing=20
            )
            
            self.play_button = Button(
                text="▶️ تشغيل",
                size_hint=(None, None),
                size=(120, 50)
            )
            self.play_button.bind(on_press=self.toggle_playback)
            controls_layout.add_widget(self.play_button)
            
            test_button = Button(
                text="🧪 اختبار",
                size_hint=(None, None),
                size=(120, 50)
            )
            test_button.bind(on_press=self.test_function)
            controls_layout.add_widget(test_button)
        
        return controls_layout
    
    def create_error_layout(self, error_message):
        """إنشاء واجهة خطأ"""
        layout = BoxLayout(orientation='vertical', padding=20, spacing=10)
        
        error_label = Label(
            text=f"❌ خطأ في التطبيق:\n{error_message}\n\n🔧 الوضع الآمن نشط",
            halign="center",
            text_size=(None, None)
        )
        layout.add_widget(error_label)
        
        return layout
    
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        info_lines = [
            "✅ التطبيق يعمل بنجاح!",
            f"🐍 Python: {sys.version.split()[0]}",
            f"📱 Kivy: {kivy.__version__}",
        ]
        
        if USE_KIVYMD:
            info_lines.append(f"🎨 KivyMD: {kivymd.__version__}")
        
        if ANDROID_AVAILABLE:
            info_lines.append("📱 Android: متاح")
        else:
            info_lines.append("💻 Desktop: متاح")
        
        if mutagen:
            info_lines.append("🎵 Mutagen: متاح")
        
        if requests:
            info_lines.append("🌐 Requests: متاح")
        
        return "\n".join(info_lines)
    
    def toggle_playback(self, instance):
        """تبديل التشغيل/الإيقاف"""
        self.is_playing = not self.is_playing
        
        if self.is_playing:
            instance.text = "⏸️ إيقاف"
            logger.info("▶️ بدء التشغيل التجريبي")
        else:
            instance.text = "▶️ تشغيل"
            logger.info("⏸️ إيقاف التشغيل")
    
    def test_function(self, instance):
        """دالة اختبار"""
        logger.info("🧪 تم الضغط على زر الاختبار")
        instance.text = "✅ نجح!"
        
        # إعادة تعيين النص بعد 2 ثانية
        Clock.schedule_once(
            lambda dt: setattr(instance, 'text', "🧪 اختبار"), 
            2
        )
    
    def update_progress(self, dt):
        """تحديث شريط التقدم"""
        if self.is_playing:
            self.progress_value += 1
            if self.progress_value > self.max_value:
                self.progress_value = 0
            
            if hasattr(self, 'progress_bar'):
                self.progress_bar.value = self.progress_value
    
    def on_start(self):
        """عند بدء التطبيق"""
        logger.info("🎯 تم بدء التطبيق بنجاح")
        
        # طلب الأذونات على Android
        if ANDROID_AVAILABLE:
            Clock.schedule_once(self.request_android_permissions, 1)
    
    def request_android_permissions(self, dt):
        """طلب أذونات Android"""
        try:
            permissions = [
                Permission.READ_EXTERNAL_STORAGE,
                Permission.WRITE_EXTERNAL_STORAGE,
                Permission.INTERNET
            ]
            request_permissions(permissions)
            logger.info("🔐 تم طلب أذونات Android")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في طلب الأذونات: {e}")
    
    def on_stop(self):
        """عند إغلاق التطبيق"""
        logger.info("🛑 إغلاق التطبيق...")

def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🚀 بدء التطبيق الرئيسي...")
        
        app = MusicPlayerApp()
        app.run()
        
        logger.info("✅ انتهى التطبيق بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في التطبيق: {e}")
        logger.error(f"📋 تفاصيل:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        sys.exit(1)
