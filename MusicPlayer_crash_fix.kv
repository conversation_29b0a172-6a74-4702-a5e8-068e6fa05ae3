#:kivy 2.0.0

# ملف KV مبسط ومقاوم للتحطم
# Simple and crash-resistant KV file

<SimpleMusicPlayer>:
    orientation: 'vertical'
    padding: dp(20)
    spacing: dp(10)
    
    # عنوان التطبيق
    Label:
        text: '🎵 مشغل الموسيقى العربي\\nArabic Music Player'
        size_hint_y: None
        height: dp(80)
        font_size: sp(20)
        halign: 'center'
        text_size: self.size
    
    # معلومات الأغنية
    Label:
        id: song_label
        text: 'لا توجد أغنية محددة\\nNo song selected'
        size_hint_y: None
        height: dp(60)
        font_size: sp(16)
        halign: 'center'
        text_size: self.size
    
    # شريط التقدم
    ProgressBar:
        id: progress_bar
        max: 100
        value: 0
        size_hint_y: None
        height: dp(20)
    
    # أزرار التحكم
    BoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(60)
        spacing: dp(10)
        
        Button:
            text: '⏮️ السابق\\nPrevious'
            font_size: sp(14)
            on_press: root.previous_song(self)
        
        Button:
            id: play_btn
            text: '▶️ تشغيل\\nPlay'
            font_size: sp(14)
            on_press: root.toggle_playback(self)
        
        Button:
            text: '⏭️ التالي\\nNext'
            font_size: sp(14)
            on_press: root.next_song(self)
    
    # مستوى الصوت
    BoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(10)
        
        Label:
            text: '🔊'
            size_hint_x: None
            width: dp(40)
            font_size: sp(16)
        
        Slider:
            id: volume_slider
            min: 0
            max: 1
            value: 0.7
            size_hint_y: None
            height: dp(40)
            on_value: root.on_volume_change(self, self.value)
    
    # حالة التطبيق
    Label:
        id: status_label
        text: 'جاري البحث عن الأغاني...\\nSearching for music files...'
        size_hint_y: None
        height: dp(40)
        font_size: sp(12)
        halign: 'center'
        text_size: self.size
