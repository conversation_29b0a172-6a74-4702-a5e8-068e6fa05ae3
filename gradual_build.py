#!/usr/bin/env python3
"""
بناء تدريجي للتطبيق - إضافة المكتبات خطوة بخطوة
Gradual build - Add libraries step by step
"""

import os
import shutil
import subprocess
import logging
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description="", timeout=1800):
    """تشغيل أمر مع معالجة الأخطاء ومهلة زمنية"""
    try:
        logger.info(f"🔄 {description}: {command}")
        start_time = time.time()
        
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        elapsed_time = time.time() - start_time
        logger.info(f"⏱️ Completed in {elapsed_time:.1f} seconds")
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            logger.error(f"❌ {description} failed")
            if result.stderr:
                logger.error(f"Error: {result.stderr[-1000:]}")  # Last 1000 chars
            return False, result.stderr
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {description} timed out after {timeout} seconds")
        return False, "Timeout"
    except Exception as e:
        logger.error(f"❌ Exception in {description}: {e}")
        return False, str(e)

def clean_build_environment():
    """تنظيف بيئة البناء"""
    logger.info("🧹 Cleaning build environment...")
    
    # Remove build directories
    dirs_to_remove = ['.buildozer', 'bin']
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                logger.info(f"✅ Removed {dir_name}")
            except Exception as e:
                logger.error(f"❌ Failed to remove {dir_name}: {e}")
    
    # Run buildozer clean
    run_command("buildozer android clean", "Buildozer clean", timeout=300)

def test_build_step(spec_file, step_name, step_description):
    """اختبار خطوة بناء واحدة"""
    logger.info(f"🔨 Testing {step_name}: {step_description}")
    
    # Copy spec file
    try:
        shutil.copy(spec_file, 'buildozer.spec')
        logger.info(f"✅ Using {spec_file}")
    except Exception as e:
        logger.error(f"❌ Failed to copy {spec_file}: {e}")
        return False
    
    # Clean environment
    clean_build_environment()
    
    # Attempt build
    success, output = run_command(
        "buildozer android debug", 
        f"Building {step_name}",
        timeout=1800  # 30 minutes
    )
    
    if success:
        # Check for APK
        apk_files = []
        if os.path.exists('bin'):
            for file in os.listdir('bin'):
                if file.endswith('.apk'):
                    apk_files.append(os.path.join('bin', file))
        
        if apk_files:
            for apk in apk_files:
                size = os.path.getsize(apk) / (1024 * 1024)  # MB
                logger.info(f"📱 APK created: {apk} ({size:.2f} MB)")
            
            # Rename APK for this step
            if apk_files:
                step_apk = f"bin/arabicplayer-{step_name}.apk"
                try:
                    shutil.copy(apk_files[0], step_apk)
                    logger.info(f"✅ Saved as {step_apk}")
                except Exception as e:
                    logger.warning(f"Could not save step APK: {e}")
        
        return True
    else:
        logger.error(f"❌ {step_name} build failed")
        return False

def analyze_build_failure():
    """تحليل أسباب فشل البناء"""
    logger.info("🔍 Analyzing build failure...")
    
    # Look for common error patterns
    log_files = []
    for root, dirs, files in os.walk('.buildozer'):
        for file in files:
            if file.endswith('.log') or 'gradle' in file.lower():
                log_files.append(os.path.join(root, file))
    
    common_errors = [
        "OutOfMemoryError",
        "BUILD FAILED",
        "FAILURE: Build failed",
        "Could not resolve",
        "Failed to apply plugin",
        "Execution failed",
        "No space left",
        "Connection timed out"
    ]
    
    for log_file in log_files[-3:]:  # Check last 3 log files
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                for error in common_errors:
                    if error in content:
                        logger.error(f"Found '{error}' in {log_file}")
                        # Extract context around error
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if error in line:
                                start = max(0, i-2)
                                end = min(len(lines), i+3)
                                context = '\n'.join(lines[start:end])
                                logger.error(f"Context:\n{context}")
                                break
        except Exception as e:
            logger.warning(f"Could not read {log_file}: {e}")

def main():
    """الدالة الرئيسية للبناء التدريجي"""
    print("🚀 Starting gradual build process...")
    print("🎯 Goal: Build APK step by step, adding libraries gradually")
    print()
    
    # Define build steps
    build_steps = [
        {
            'name': 'minimal',
            'spec': 'buildozer_minimal_stable.spec',
            'description': 'Core libraries only (Kivy + KivyMD + Mutagen)'
        },
        {
            'name': 'arabic',
            'spec': 'buildozer_step2.spec', 
            'description': 'Add Arabic text support'
        }
    ]
    
    successful_steps = []
    failed_steps = []
    
    for i, step in enumerate(build_steps, 1):
        print(f"\n{'='*60}")
        print(f"📋 STEP {i}: {step['name'].upper()}")
        print(f"📝 {step['description']}")
        print('='*60)
        
        if not os.path.exists(step['spec']):
            logger.error(f"❌ Spec file not found: {step['spec']}")
            failed_steps.append(step)
            continue
        
        success = test_build_step(step['spec'], step['name'], step['description'])
        
        if success:
            successful_steps.append(step)
            logger.info(f"🎉 Step {i} ({step['name']}) completed successfully!")
        else:
            failed_steps.append(step)
            logger.error(f"❌ Step {i} ({step['name']}) failed!")
            
            # Analyze failure
            analyze_build_failure()
            
            # Ask if user wants to continue
            print(f"\n💡 Step {i} failed. Options:")
            print("1. Continue to next step")
            print("2. Stop here and investigate")
            print("3. Try with even simpler config")
            
            # For automation, continue to next step
            logger.info("Continuing to next step...")
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 BUILD SUMMARY")
    print('='*60)
    
    if successful_steps:
        print("✅ Successful steps:")
        for step in successful_steps:
            print(f"   ✅ {step['name']}: {step['description']}")
    
    if failed_steps:
        print("\n❌ Failed steps:")
        for step in failed_steps:
            print(f"   ❌ {step['name']}: {step['description']}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    
    if successful_steps:
        last_success = successful_steps[-1]
        print(f"✅ Use the last successful build: {last_success['spec']}")
        print(f"📱 APK available: bin/arabicplayer-{last_success['name']}.apk")
        
        if failed_steps:
            print("🔧 For failed steps:")
            print("   1. Check error logs above")
            print("   2. Try with fewer libraries")
            print("   3. Check internet connection")
            print("   4. Increase timeout or memory")
    else:
        print("❌ All builds failed. Suggestions:")
        print("   1. Check basic Kivy installation")
        print("   2. Try with Python 3.9 instead of 3.11")
        print("   3. Use older, more stable library versions")
        print("   4. Check available disk space")
    
    print(f"\n🎯 Next steps:")
    if successful_steps:
        print("   1. Test the successful APK on device")
        print("   2. Gradually add more libraries")
        print("   3. Debug specific library conflicts")
    else:
        print("   1. Check system requirements")
        print("   2. Try on different platform")
        print("   3. Use Docker for isolated build")

if __name__ == "__main__":
    main()
