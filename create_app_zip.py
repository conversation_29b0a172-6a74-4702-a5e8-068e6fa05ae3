#!/usr/bin/env python3
"""
إنشاء ملف ZIP شامل لمشغل الموسيقى العربي
Create comprehensive ZIP file for Arabic Music Player
"""

import os
import zipfile
import logging
import json
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_comprehensive_zip():
    """إنشاء ملف ZIP شامل للتطبيق"""
    
    zip_filename = f"arabic_music_player_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    # الملفات الأساسية للتطبيق
    core_files = [
        "main.py",
        "permission_manager.py",
        "MusicPlayer.kv",
        "arabic_utils.py"
    ]
    
    # ملفات المكتبات والمكونات
    library_files = [
        "custom_slider.py",
        "playing_indicator.py",
        "color_circle.py",
        "audio_enhancer.py",
        "download_manager.py",
        "download_screen.py",
        "search_screen.py",
        "performance_optimizer.py"
    ]
    
    # ملفات KV للواجهة
    kv_files = [
        "MusicPlayer.kv",
        "download_screen.kv", 
        "search_screen.kv",
        "audio_settings.kv",
        "modern_components.kv",
        "MusicPlayer_simple_test.kv"
    ]
    
    # ملفات إعدادات Buildozer
    buildozer_files = [
        "buildozer_compatible_fixed.spec",
        "buildozer_minimal_stable.spec",
        "buildozer_step2.spec",
        "buildozer_full_compatible.spec",
        "buildozer_ndk25.spec",
        "buildozer_gradle_fix.spec"
    ]
    
    # ملفات الاختبار والأدوات
    tool_files = [
        "test_permissions.py",
        "main_ui_test.py",
        "main_gradle_test.py",
        "gradual_build.py",
        "fix_ndk_api_issue.py",
        "fix_ui_layout.py",
        "upgrade_to_full_version.py",
        "create_app_zip.py"
    ]
    
    # ملفات التوثيق والأدلة
    documentation_files = [
        "PERMISSIONS_TROUBLESHOOTING.md",
        "DEVICE_COMPATIBILITY_GUIDE.md",
        "GRADLE_DEBUG_GUIDE.md",
        "UI_TROUBLESHOOTING.md",
        "FULL_VERSION_GUIDE.md",
        "QUICK_NDK_FIX.md",
        "IMPROVED_VERSION_SUMMARY.md",
        "ANDROID_CRASH_ANALYSIS_REPORT.md"
    ]
    
    # مجلدات الموارد
    resource_directories = [
        "fonts",
        "images", 
        "assets",
        "default_covers"
    ]
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            logger.info(f"Creating comprehensive ZIP: {zip_filename}")
            
            # إضافة الملفات الأساسية
            logger.info("Adding core files...")
            for file_path in core_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, f"core/{file_path}")
                    logger.info(f"✅ Added core file: {file_path}")
                else:
                    logger.warning(f"❌ Core file not found: {file_path}")
            
            # إضافة ملفات المكتبات
            logger.info("Adding library files...")
            for file_path in library_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, f"libraries/{file_path}")
                    logger.info(f"✅ Added library: {file_path}")
                else:
                    logger.warning(f"❌ Library not found: {file_path}")
            
            # إضافة ملفات KV
            logger.info("Adding KV files...")
            for file_path in kv_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, f"ui/{file_path}")
                    logger.info(f"✅ Added KV file: {file_path}")
                else:
                    logger.warning(f"❌ KV file not found: {file_path}")
            
            # إضافة ملفات Buildozer
            logger.info("Adding Buildozer configurations...")
            for file_path in buildozer_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, f"buildozer_configs/{file_path}")
                    logger.info(f"✅ Added buildozer config: {file_path}")
                else:
                    logger.warning(f"❌ Buildozer config not found: {file_path}")
            
            # إضافة أدوات الاختبار
            logger.info("Adding testing tools...")
            for file_path in tool_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, f"tools/{file_path}")
                    logger.info(f"✅ Added tool: {file_path}")
                else:
                    logger.warning(f"❌ Tool not found: {file_path}")
            
            # إضافة التوثيق
            logger.info("Adding documentation...")
            for file_path in documentation_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, f"docs/{file_path}")
                    logger.info(f"✅ Added documentation: {file_path}")
                else:
                    logger.warning(f"❌ Documentation not found: {file_path}")
            
            # إضافة مجلدات الموارد
            logger.info("Adding resource directories...")
            for dir_path in resource_directories:
                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                    for root, dirs, files in os.walk(dir_path):
                        for file in files:
                            # تجاهل الملفات غير المرغوبة
                            if file.endswith(('.pyc', '.pyo', '.pyd', '.DS_Store')):
                                continue
                            if file.startswith('.'):
                                continue
                                
                            file_path = os.path.join(root, file)
                            arcname = f"resources/{os.path.relpath(file_path)}"
                            zipf.write(file_path, arcname)
                            logger.info(f"✅ Added resource: {arcname}")
                else:
                    logger.warning(f"❌ Resource directory not found: {dir_path}")
            
            # إنشاء ملف README شامل
            readme_content = create_comprehensive_readme()
            zipf.writestr("README.md", readme_content)
            logger.info("✅ Added comprehensive README.md")
            
            # إنشاء ملف تعليمات البناء
            build_instructions = create_build_instructions()
            zipf.writestr("BUILD_INSTRUCTIONS.md", build_instructions)
            logger.info("✅ Added BUILD_INSTRUCTIONS.md")
            
            # إنشاء ملف معلومات الإصدار
            version_info = create_version_info()
            zipf.writestr("VERSION_INFO.json", json.dumps(version_info, indent=2, ensure_ascii=False))
            logger.info("✅ Added VERSION_INFO.json")
            
            # إنشاء سكريبت إعداد سريع
            setup_script = create_setup_script()
            zipf.writestr("quick_setup.py", setup_script)
            logger.info("✅ Added quick_setup.py")
        
        # معلومات الملف المنشأ
        file_size = os.path.getsize(zip_filename)
        logger.info(f"🎉 Successfully created {zip_filename}")
        logger.info(f"📦 ZIP file size: {file_size / (1024*1024):.2f} MB")
        
        return zip_filename
        
    except Exception as e:
        logger.error(f"❌ Error creating ZIP file: {e}")
        return None

def create_comprehensive_readme():
    """إنشاء ملف README شامل"""
    return """# 🎵 مشغل الموسيقى العربي - Arabic Music Player

## 📋 نظرة عامة

مشغل موسيقى متقدم مصمم خصيصاً للنصوص العربية مع دعم شامل لأجهزة الأندرويد.

### ✨ الميزات الرئيسية

- 🎵 **تشغيل موسيقى متقدم** - دعم جميع صيغ الصوت الشائعة
- 🔤 **دعم النصوص العربية** - عرض صحيح للنصوص العربية وثنائية الاتجاه
- 🌐 **البحث عبر الإنترنت** - البحث والتشغيل من YouTube وخدمات أخرى
- ⬇️ **مدير التحميل** - تحميل الأغاني للتشغيل دون اتصال
- 🎛️ **إكوالايزر متقدم** - تحسين جودة الصوت مع إعدادات مسبقة
- 📱 **توافق واسع** - يعمل على Android 5.0+ (95% من الأجهزة)
- 🔐 **إدارة أذونات ذكية** - طلب وإدارة الأذونات تلقائياً
- ⚡ **أداء محسن** - تكيف تلقائي مع قدرات الجهاز

## 📁 هيكل المشروع

```
arabic_music_player_complete/
├── core/                    # الملفات الأساسية
│   ├── main.py             # الملف الرئيسي للتطبيق
│   ├── permission_manager.py # مدير الأذونات
│   └── arabic_utils.py     # أدوات النصوص العربية
├── libraries/              # المكتبات والمكونات
│   ├── audio_enhancer.py   # محسن الصوت
│   ├── download_manager.py # مدير التحميل
│   └── ...
├── ui/                     # ملفات الواجهة
│   ├── MusicPlayer.kv      # الواجهة الرئيسية
│   └── ...
├── buildozer_configs/      # إعدادات البناء
│   ├── buildozer_compatible_fixed.spec
│   └── ...
├── tools/                  # أدوات التطوير والاختبار
│   ├── test_permissions.py
│   ├── gradual_build.py
│   └── ...
├── docs/                   # التوثيق والأدلة
│   ├── PERMISSIONS_TROUBLESHOOTING.md
│   └── ...
└── resources/              # الموارد (خطوط، صور)
    ├── fonts/
    └── images/
```

## 🚀 البدء السريع

### الطريقة 1: الإعداد التلقائي
```bash
python quick_setup.py
```

### الطريقة 2: الإعداد اليدوي
```bash
# 1. نسخ الملفات الأساسية
cp core/* .
cp libraries/* .
cp ui/* .

# 2. اختيار إعدادات البناء
cp buildozer_configs/buildozer_compatible_fixed.spec buildozer.spec

# 3. بناء التطبيق
buildozer android clean
buildozer android debug
```

## 📱 متطلبات النظام

### للتطوير:
- Python 3.11+
- Buildozer
- Android SDK/NDK (يتم تحميلها تلقائياً)
- 8GB+ مساحة فارغة
- 4GB+ RAM

### للتشغيل:
- Android 5.0+ (API 21+)
- 100MB+ مساحة فارغة
- أذونات التخزين والإنترنت

## 🛠️ خيارات البناء

### البناء الأساسي (موصى به للبداية)
```bash
cp buildozer_configs/buildozer_minimal_stable.spec buildozer.spec
buildozer android debug
```

### البناء الكامل (جميع الميزات)
```bash
cp buildozer_configs/buildozer_compatible_fixed.spec buildozer.spec
buildozer android debug
```

### البناء التدريجي (للتشخيص)
```bash
python tools/gradual_build.py
```

## 🔧 استكشاف الأخطاء

### مشاكل البناء:
- راجع `docs/GRADLE_DEBUG_GUIDE.md`
- استخدم `tools/gradual_build.py`

### مشاكل الأذونات:
- راجع `docs/PERMISSIONS_TROUBLESHOOTING.md`
- استخدم `tools/test_permissions.py`

### مشاكل الواجهة:
- راجع `docs/UI_TROUBLESHOOTING.md`
- استخدم `tools/fix_ui_layout.py`

## 📊 إحصائيات التوافق

- ✅ **Android 5.0+**: 95% من الأجهزة النشطة
- ✅ **جميع المعماريات**: ARM 32/64-bit
- ✅ **أجهزة قديمة**: تحسينات خاصة للأداء
- ✅ **أجهزة حديثة**: استخدام كامل للميزات

## 🎯 الإصدارات المتاحة

1. **الإصدار الأساسي** - تشغيل موسيقى أساسي
2. **إصدار النصوص العربية** - دعم كامل للعربية
3. **الإصدار الكامل** - جميع الميزات المتقدمة

## 📞 الدعم والمساعدة

- 📖 **التوثيق**: مجلد `docs/`
- 🛠️ **أدوات التشخيص**: مجلد `tools/`
- 🔍 **أدلة استكشاف الأخطاء**: ملفات `.md`

## 🎉 شكر خاص

تم تطوير هذا التطبيق مع التركيز على:
- دعم ممتاز للنصوص العربية
- توافق واسع مع أجهزة الأندرويد
- أداء محسن وإدارة ذاكرة ذكية
- واجهة مستخدم حديثة وسهلة الاستخدام

**🚀 جاهز للاستخدام والتطوير!**
"""

def create_build_instructions():
    """إنشاء تعليمات البناء"""
    return """# 🏗️ تعليمات البناء - Build Instructions

## 🎯 خيارات البناء السريع

### 1. البناء الأساسي (للمبتدئين)
```bash
# نسخ الملفات
cp core/main.py .
cp core/permission_manager.py .
cp ui/MusicPlayer.kv .
cp buildozer_configs/buildozer_minimal_stable.spec buildozer.spec

# البناء
buildozer android clean
buildozer android debug
```

### 2. البناء الكامل (جميع الميزات)
```bash
# الإعداد التلقائي
python quick_setup.py --full

# أو يدوياً
cp core/* .
cp libraries/* .
cp ui/* .
cp buildozer_configs/buildozer_compatible_fixed.spec buildozer.spec

buildozer android clean
buildozer android debug
```

### 3. البناء التدريجي (للتشخيص)
```bash
python tools/gradual_build.py
```

## 🔧 حل المشاكل الشائعة

### مشكلة: NDK API غير متوافق
```bash
python tools/fix_ndk_api_issue.py
```

### مشكلة: فشل Gradle
```bash
# استخدم الإعدادات المبسطة
cp buildozer_configs/buildozer_minimal_stable.spec buildozer.spec
```

### مشكلة: مشاكل الواجهة
```bash
python tools/fix_ui_layout.py
```

### مشكلة: الأذونات لا تعمل
```bash
# اختبار الأذونات
python tools/test_permissions.py
```

## 📋 قائمة فحص قبل البناء

- [ ] Python 3.11+ مثبت
- [ ] Buildozer مثبت
- [ ] 8GB+ مساحة فارغة
- [ ] اتصال إنترنت مستقر
- [ ] جميع الملفات الأساسية موجودة

## ⏱️ أوقات البناء المتوقعة

- **البناء الأول**: 15-30 دقيقة
- **البناء التالي**: 5-10 دقائق
- **البناء بعد التنظيف**: 10-20 دقيقة

## 🎉 علامات النجاح

```
BUILD SUCCESSFUL in 8m 23s
✅ APK created: bin/arabicplayer-1.0-debug.apk
📱 Size: ~25 MB
🚀 Ready for testing!
```
"""

def create_version_info():
    """إنشاء معلومات الإصدار"""
    return {
        "app_name": "Arabic Music Player",
        "version": "1.0.0",
        "build_date": datetime.now().isoformat(),
        "target_android": "5.0+ (API 21+)",
        "python_version": "3.11.6",
        "kivy_version": "2.3.0",
        "kivymd_version": "1.1.1",
        "features": [
            "Arabic text support",
            "Online music search",
            "Download manager", 
            "Audio enhancer",
            "Permission manager",
            "Device compatibility detection",
            "Performance optimization"
        ],
        "supported_formats": [
            "MP3", "WAV", "OGG", "M4A", "FLAC"
        ],
        "languages": [
            "Arabic", "English"
        ],
        "architectures": [
            "armeabi-v7a", "arm64-v8a"
        ]
    }

def create_setup_script():
    """إنشاء سكريبت الإعداد السريع"""
    return '''#!/usr/bin/env python3
"""
سكريبت الإعداد السريع لمشغل الموسيقى العربي
Quick setup script for Arabic Music Player
"""

import os
import shutil
import sys
import argparse

def setup_basic():
    """إعداد أساسي"""
    print("🔧 Setting up basic version...")
    
    # نسخ الملفات الأساسية
    files_to_copy = [
        ("core/main.py", "main.py"),
        ("core/permission_manager.py", "permission_manager.py"),
        ("ui/MusicPlayer.kv", "MusicPlayer.kv"),
        ("buildozer_configs/buildozer_minimal_stable.spec", "buildozer.spec")
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy(src, dst)
            print(f"✅ Copied {src} -> {dst}")
        else:
            print(f"❌ Not found: {src}")

def setup_full():
    """إعداد كامل"""
    print("🚀 Setting up full version...")
    
    # نسخ جميع الملفات
    directories = ["core", "libraries", "ui"]
    
    for directory in directories:
        if os.path.exists(directory):
            for file in os.listdir(directory):
                if file.endswith('.py') or file.endswith('.kv'):
                    src = os.path.join(directory, file)
                    dst = file
                    shutil.copy(src, dst)
                    print(f"✅ Copied {src} -> {dst}")
    
    # نسخ إعدادات البناء الكاملة
    shutil.copy("buildozer_configs/buildozer_compatible_fixed.spec", "buildozer.spec")
    print("✅ Copied full buildozer configuration")

def main():
    parser = argparse.ArgumentParser(description="Quick setup for Arabic Music Player")
    parser.add_argument("--full", action="store_true", help="Setup full version with all features")
    parser.add_argument("--basic", action="store_true", help="Setup basic version")
    
    args = parser.parse_args()
    
    if args.full:
        setup_full()
    elif args.basic:
        setup_basic()
    else:
        print("🎯 Choose setup type:")
        print("1. Basic version (recommended for first build)")
        print("2. Full version (all features)")
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            setup_basic()
        elif choice == "2":
            setup_full()
        else:
            print("❌ Invalid choice")
            return
    
    print("\\n🎉 Setup completed!")
    print("📋 Next steps:")
    print("   1. buildozer android clean")
    print("   2. buildozer android debug")
    print("   3. Test the APK on your device")

if __name__ == "__main__":
    main()
'''

def main():
    """الدالة الرئيسية"""
    print("🚀 Creating comprehensive ZIP file for Arabic Music Player...")
    print("=" * 60)
    
    result = create_comprehensive_zip()
    
    if result:
        print(f"\n🎉 SUCCESS! Created comprehensive ZIP file:")
        print(f"📦 File: {result}")
        print(f"📁 Contains:")
        print("   ✅ Core application files")
        print("   ✅ All libraries and components") 
        print("   ✅ UI files and resources")
        print("   ✅ Multiple buildozer configurations")
        print("   ✅ Testing and debugging tools")
        print("   ✅ Comprehensive documentation")
        print("   ✅ Quick setup script")
        print("\n🚀 Ready for distribution and development!")
        print("\n📋 To use:")
        print("   1. Extract the ZIP file")
        print("   2. Run: python quick_setup.py")
        print("   3. Build: buildozer android debug")
    else:
        print("\n❌ FAILED to create ZIP file")
        print("Check the logs above for details")

if __name__ == "__main__":
    main()
