#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح سريع لمشكلة NDK 25
Quick Fix for NDK 25 Issue
"""

import os
import shutil

def fix_ndk_issue():
    """إصلاح مشكلة NDK 25"""
    
    print("🔧 إصلاح مشكلة NDK 25...")
    print("❌ المشكلة: The minimum supported NDK version is 25")
    print("✅ الحل: تحديث buildozer.spec لاستخدام NDK 25b")
    
    # نسخ ملف buildozer.spec المحدث
    if os.path.exists('buildozer_ndk25_fixed.spec'):
        shutil.copy('buildozer_ndk25_fixed.spec', 'buildozer.spec')
        print("✅ تم تحديث buildozer.spec لاستخدام NDK 25b")
    else:
        print("❌ ملف buildozer_ndk25_fixed.spec غير موجود")
        return False
    
    # تنظيف البناء السابق
    print("🧹 تنظيف البناء السابق...")
    
    folders_to_clean = ['.buildozer', 'bin', '__pycache__']
    for folder in folders_to_clean:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🗑️ تم حذف: {folder}")
    
    # عرض التغييرات
    print("\n📋 التغييرات المطبقة:")
    print("  🔧 NDK: 23c → 25b")
    print("  📱 API: 33 → 34 (Android 14)")
    print("  🐍 Python: 3.11.6 (بدون تغيير)")
    print("  📦 المكتبات: محسنة ومتوافقة")
    
    print("\n🎯 الآن يمكنك إعادة البناء:")
    print("  echo 'y' | buildozer android debug")
    
    return True

if __name__ == "__main__":
    success = fix_ndk_issue()
    if success:
        print("\n✅ تم إصلاح المشكلة بنجاح!")
    else:
        print("\n❌ فشل في إصلاح المشكلة!")
