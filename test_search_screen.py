import logging
logging.basicConfig(level=logging.DEBUG)

from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.app import MDApp
from search_screen import SearchScreen

# Set window size for development
Window.size = (400, 700)

class TestApp(MDApp):
    def build(self):
        # Set theme properties
        self.theme_cls.primary_palette = "DeepPurple"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Dark"

        try:
            # Load the search screen KV file
            print("Loading search_screen.kv...")
            Builder.load_file("search_screen.kv")
            print("Successfully loaded search_screen.kv")

            # Create and return the search screen
            screen = SearchScreen()
            print("Created SearchScreen instance")
            return screen
        except Exception as e:
            print(f"Error in build: {e}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == '__main__':
    print("Starting TestApp...")
    TestApp().run()
