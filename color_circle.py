from kivymd.uix.behaviors import Circular<PERSON><PERSON>ple<PERSON>ehavior
from kivy.uix.behaviors import Button<PERSON>ehavior
from kivy.uix.widget import Widget
from kivy.graphics import Color, Ellipse
from kivy.metrics import dp
from kivy.clock import Clock

class ColorCircle(CircularRippleBehavior, ButtonBehavior, Widget):
    def __init__(self, **kwargs):
        self.theme_color = kwargs.pop('color', [1, 1, 1, 1])
        super().__init__(**kwargs)
        self.size_hint = (None, None)
        self.size = (dp(30), dp(30))
        Clock.schedule_once(self.draw_circle, 0)

    def draw_circle(self, *args):
        self.canvas.before.clear()
        with self.canvas.before:
            Color(*self.theme_color)
            Ellipse(pos=self.pos, size=self.size)

    def on_size(self, *args):
        self.draw_circle()

    def on_pos(self, *args):
        self.draw_circle()
        
    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
