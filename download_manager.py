"""
Download Manager for Music Player Application
Handles downloading music files from URLs and tracking download progress
"""

import os
import threading
import time
import re
import logging
import json
from urllib.parse import urlparse, unquote
import requests
from kivy.event import EventDispatcher
from kivy.properties import StringProperty, NumericProperty, BooleanProperty, ListProperty, DictProperty, ObjectProperty
from kivy.clock import Clock, mainthread

# Import libraries for YouTube downloads
try:
    from pytube import YouTube
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False
    logging.warning("pytube not available. Will try alternative methods.")

# Try to import yt-dlp as a more reliable alternative
try:
    import yt_dlp
    YTDLP_AVAILABLE = True
except ImportError:
    YTDLP_AVAILABLE = False
    logging.warning("yt-dlp not available. Will try alternative methods.")

# At least one of the libraries should be available for YouTube downloads
YOUTUBE_DOWNLOAD_AVAILABLE = PYTUBE_AVAILABLE or YTDLP_AVAILABLE

# Configure logging
logger = logging.getLogger(__name__)

class VideoFormat(EventDispatcher):
    """Class representing a video format option"""
    itag = NumericProperty(0)
    resolution = StringProperty("")
    extension = StringProperty("")
    file_size = NumericProperty(0)
    is_audio_only = BooleanProperty(False)
    is_video_only = BooleanProperty(False)
    description = StringProperty("")

    def __init__(self, itag=0, resolution="", extension="", file_size=0,
                 is_audio_only=False, is_video_only=False, **kwargs):
        super(VideoFormat, self).__init__(**kwargs)
        self.itag = itag
        self.resolution = resolution
        self.extension = extension
        self.file_size = file_size
        self.is_audio_only = is_audio_only
        self.is_video_only = is_video_only

        # Create a description for the format
        if is_audio_only:
            self.description = f"Audio only ({extension}) - {self.get_formatted_size()}"
        elif is_video_only:
            self.description = f"Video only - {resolution} ({extension}) - {self.get_formatted_size()}"
        else:
            self.description = f"Video with audio - {resolution} ({extension}) - {self.get_formatted_size()}"

    def get_formatted_size(self):
        """Return formatted file size"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"

class DownloadItem(EventDispatcher):
    """Class representing a single download item"""
    url = StringProperty("")
    filename = StringProperty("")
    status = StringProperty("pending")  # pending, downloading, completed, error, paused, canceled
    progress = NumericProperty(0)  # 0-100
    download_path = StringProperty("")
    error_message = StringProperty("")
    file_size = NumericProperty(0)
    downloaded_size = NumericProperty(0)
    speed = NumericProperty(0)  # bytes per second
    start_time = NumericProperty(0)
    is_selected = BooleanProperty(False)
    is_youtube = BooleanProperty(False)
    available_formats = ListProperty([])
    selected_format = ObjectProperty(None, allownone=True)
    needs_mp3_conversion = BooleanProperty(False)  # Flag for MP3 conversion
    download_date = StringProperty("")  # Date and time when download was completed

    def __init__(self, url, download_path, **kwargs):
        super(DownloadItem, self).__init__(**kwargs)
        self.url = url
        self.download_path = download_path

        # Initialize download date
        self.download_date = time.strftime('%Y-%m-%d %H:%M:%S')

        # Check if this is a YouTube URL
        self.is_youtube = self._is_youtube_url(url)

        # Extract filename from URL
        parsed_url = urlparse(url)
        filename = os.path.basename(unquote(parsed_url.path))

        # If no filename in URL, use a default name
        if not filename or '.' not in filename:
            filename = f"download_{int(time.time())}.mp3"

        # Sanitize filename to remove problematic characters
        filename = self._sanitize_filename(filename)

        self.filename = filename
        self.full_path = os.path.join(download_path, filename)

    def _sanitize_filename(self, filename):
        """Sanitize filename to ensure it's valid for the file system"""
        try:
            # Replace problematic characters with underscores
            # Keep the extension intact
            name, ext = os.path.splitext(filename)

            # Replace characters that might cause issues in filenames
            # This is a more conservative approach than completely removing non-ASCII
            invalid_chars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|']
            for char in invalid_chars:
                name = name.replace(char, '_')

            # Limit filename length
            if len(name) > 100:
                name = name[:100]

            # Ensure the filename is not empty
            if not name:
                name = f"download_{int(time.time())}"

            # Reconstruct the filename with extension
            sanitized = name + ext

            logger.info(f"Sanitized filename: '{filename}' -> '{sanitized}'")
            return sanitized
        except Exception as e:
            logger.error(f"Error sanitizing filename: {e}")
            # Return a safe default if sanitization fails
            return f"download_{int(time.time())}.mp3"

    def _is_youtube_url(self, url):
        """Check if the URL is a YouTube URL"""
        # List of YouTube domains
        youtube_domains = [
            'youtube.com',
            'youtu.be',
            'www.youtube.com',
            'm.youtube.com',
            'youtube-nocookie.com',
            'www.youtube-nocookie.com'
        ]

        try:
            # Parse the URL
            parsed_url = urlparse(url)

            # Check if the domain is a YouTube domain
            is_youtube_domain = any(domain in parsed_url.netloc for domain in youtube_domains)

            # Additional check for YouTube video ID patterns
            if is_youtube_domain:
                return True

            # Check for YouTube video ID in the URL (for cases where domain check might fail)
            youtube_patterns = [
                r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',  # Standard YouTube video ID pattern
                r'^([0-9A-Za-z_-]{11})$',           # Just the video ID
                r'youtu\.be\/([0-9A-Za-z_-]{11})',  # Short URL format
                r'embed\/([0-9A-Za-z_-]{11})'       # Embed format
            ]

            for pattern in youtube_patterns:
                if re.search(pattern, url):
                    return True

            return False
        except Exception as e:
            logger.warning(f"Error checking if URL is YouTube: {e}")
            # Default to False if there's an error
            return False

    def fetch_youtube_formats(self):
        """Fetch available formats for a YouTube video"""
        if not self.is_youtube or not (PYTUBE_AVAILABLE or YTDLP_AVAILABLE):
            return []

        # Set status to indicate we're fetching formats
        self.status = "fetching_formats"

        # Try using yt-dlp first (more reliable)
        if YTDLP_AVAILABLE:
            try:
                return self._fetch_formats_with_ytdlp()
            except Exception as e:
                logger.warning(f"Error fetching formats with yt-dlp: {e}. Trying pytube.")
                # If yt-dlp fails, try pytube if available
                if PYTUBE_AVAILABLE:
                    try:
                        return self._fetch_formats_with_pytube()
                    except Exception as e2:
                        logger.error(f"Error fetching formats with pytube: {e2}")
                        return self._get_fallback_formats()
                else:
                    return self._get_fallback_formats()
        # If yt-dlp is not available, try pytube
        elif PYTUBE_AVAILABLE:
            try:
                return self._fetch_formats_with_pytube()
            except Exception as e:
                logger.error(f"Error fetching formats with pytube: {e}")
                return self._get_fallback_formats()
        else:
            # Neither library is available
            self.error_message = "No YouTube download library available"
            self.status = "error"
            return []

    def _fetch_formats_with_ytdlp(self):
        """Fetch formats using yt-dlp"""
        formats = []

        try:
            # Configure yt-dlp options
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'skip_download': True,  # We just want the formats, not to download
                'listformats': True,
                'noplaylist': True,
            }

            # Create a YoutubeDL object
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Extract info
                info = ydl.extract_info(self.url, download=False)

                # Get video title for filename
                video_title = info.get('title', '')
                if video_title:
                    # Clean the title to make it a valid filename
                    video_title = re.sub(r'[\\/*?:"<>|]', "", video_title)
                    self.filename = f"{video_title}.mp4"
                    self.full_path = os.path.join(self.download_path, self.filename)

                # Process available formats
                available_formats = info.get('formats', [])

                # Add MP3 audio option (this is a special format that will be converted)
                mp3_format = VideoFormat(
                    itag=9999,  # Special ID for MP3 conversion
                    resolution="",
                    extension="mp3",
                    file_size=0,  # We don't know the size yet
                    is_audio_only=True
                )
                mp3_format.description = "Audio only (MP3) - Best quality"
                formats.append(mp3_format)

                # Add WAV audio option (this is a special format that will be converted)
                wav_format = VideoFormat(
                    itag=9998,  # Special ID for WAV conversion
                    resolution="",
                    extension="wav",
                    file_size=0,
                    is_audio_only=True
                )
                wav_format.description = "Audio only (WAV) - High quality"
                formats.append(wav_format)

                # Add video formats with audio (only MP4)
                video_formats = [f for f in available_formats if f.get('vcodec') != 'none' and f.get('acodec') != 'none' and f.get('ext') == 'mp4']

                # Sort by resolution (highest first)
                video_formats.sort(key=lambda x: x.get('height', 0) or 0, reverse=True)

                # Take only the top 3 resolutions to avoid cluttering the UI
                top_resolutions = []
                added_resolutions = set()

                for fmt in video_formats:
                    format_id = fmt.get('format_id', '0')
                    resolution = fmt.get('height', 0)
                    if resolution:
                        resolution_str = f"{resolution}p"

                        # Only add if we haven't added this resolution yet
                        if resolution_str not in added_resolutions:
                            added_resolutions.add(resolution_str)

                            file_size = fmt.get('filesize', 0) or 0

                            format_obj = VideoFormat(
                                itag=int(format_id) if format_id.isdigit() else 0,
                                resolution=resolution_str,
                                extension='mp4',
                                file_size=file_size
                            )
                            format_obj.description = f"Video (MP4) - {resolution_str}"
                            top_resolutions.append(format_obj)

                            # Limit to 3 resolutions
                            if len(top_resolutions) >= 3:
                                break

                # Add the top resolutions to the formats list
                formats.extend(top_resolutions)

            # If no formats were found, add fallbacks
            if not formats:
                formats = self._get_fallback_formats()

            # Update available formats
            self.available_formats = formats

            # Set status back to pending
            self.status = "pending"

            return formats

        except Exception as e:
            logger.error(f"Error in _fetch_formats_with_ytdlp: {e}")
            raise

    def _fetch_formats_with_pytube(self):
        """Fetch formats using pytube"""
        formats = []

        try:
            # Create YouTube object with additional options for better error handling
            yt = YouTube(
                self.url,
                use_oauth=False,
                allow_oauth_cache=True,
                on_progress_callback=None,
                on_complete_callback=None,
                proxies=None
            )

            # Add a fallback for common errors
            if not yt:
                raise Exception("Failed to create YouTube object")

            # Get video title for filename
            video_title = yt.title
            if video_title:
                # Clean the title to make it a valid filename
                video_title = re.sub(r'[\\/*?:"<>|]', "", video_title)
                self.filename = f"{video_title}.mp4"
                self.full_path = os.path.join(self.download_path, self.filename)

            # Add MP3 audio option (this is a special format that will be converted)
            mp3_format = VideoFormat(
                itag=9999,  # Special ID for MP3 conversion
                resolution="",
                extension="mp3",
                file_size=0,  # We don't know the size yet
                is_audio_only=True
            )
            mp3_format.description = "Audio only (MP3) - Best quality"
            formats.append(mp3_format)

            # Add WAV audio option (this is a special format that will be converted)
            wav_format = VideoFormat(
                itag=9998,  # Special ID for WAV conversion
                resolution="",
                extension="wav",
                file_size=0,
                is_audio_only=True
            )
            wav_format.description = "Audio only (WAV) - High quality"
            formats.append(wav_format)

            # Try to get actual streams
            try:
                # Add video streams with audio (only MP4)
                mp4_streams = []
                for stream in yt.streams.filter(progressive=True, file_extension='mp4').order_by('resolution').desc():
                    try:
                        file_size = stream.filesize
                    except:
                        file_size = 0

                    # Only add if we have a resolution
                    if stream.resolution:
                        mp4_streams.append({
                            'itag': stream.itag,
                            'resolution': stream.resolution,
                            'file_size': file_size
                        })

                # Take only the top 3 resolutions to avoid cluttering the UI
                added_resolutions = set()
                for stream_info in mp4_streams:
                    resolution = stream_info['resolution']

                    # Only add if we haven't added this resolution yet
                    if resolution not in added_resolutions:
                        added_resolutions.add(resolution)

                        format_obj = VideoFormat(
                            itag=stream_info['itag'],
                            resolution=resolution,
                            extension='mp4',
                            file_size=stream_info['file_size']
                        )
                        format_obj.description = f"Video (MP4) - {resolution}"
                        formats.append(format_obj)

                        # Limit to 3 resolutions
                        if len(added_resolutions) >= 3:
                            break
            except Exception as stream_error:
                logger.warning(f"Error getting streams: {stream_error}. Using default formats.")
                # If we can't get streams, we'll use the default format added earlier

            # Update available formats
            self.available_formats = formats

            # Set status back to pending
            self.status = "pending"

            return formats

        except Exception as e:
            logger.error(f"Error in _fetch_formats_with_pytube: {e}")
            raise

    def _get_fallback_formats(self):
        """Get fallback formats when all other methods fail"""
        fallback_formats = []

        # Add MP3 audio option (this is a special format that will be converted)
        mp3_format = VideoFormat(
            itag=9999,  # Special ID for MP3 conversion
            resolution="",
            extension="mp3",
            file_size=0,  # We don't know the size yet
            is_audio_only=True
        )
        mp3_format.description = "Audio only (MP3) - Best quality"
        fallback_formats.append(mp3_format)

        # Add WAV audio option
        wav_format = VideoFormat(
            itag=9998,  # Special ID for WAV conversion
            resolution="",
            extension="wav",
            file_size=0,
            is_audio_only=True
        )
        wav_format.description = "Audio only (WAV) - High quality"
        fallback_formats.append(wav_format)

        # Add default video format (MP4)
        video_format = VideoFormat(
            itag=18,  # Default video format (mp4, 360p)
            resolution="360p",
            extension="mp4",
            file_size=0
        )
        video_format.description = "Video (MP4) - 360p"
        fallback_formats.append(video_format)

        # Add high quality video format (MP4)
        hd_video_format = VideoFormat(
            itag=22,  # Default HD video format (mp4, 720p)
            resolution="720p",
            extension="mp4",
            file_size=0
        )
        hd_video_format.description = "Video (MP4) - 720p HD"
        fallback_formats.append(hd_video_format)

        # Update available formats with fallbacks
        self.available_formats = fallback_formats
        self.status = "pending"  # Set back to pending so user can select format

        return fallback_formats

    def get_formatted_size(self):
        """Return formatted file size"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"

    def get_formatted_speed(self):
        """Return formatted download speed"""
        if self.speed < 1024:
            return f"{self.speed:.1f} B/s"
        elif self.speed < 1024 * 1024:
            return f"{self.speed / 1024:.1f} KB/s"
        else:
            return f"{self.speed / (1024 * 1024):.1f} MB/s"

    def get_estimated_time(self):
        """Return estimated time remaining"""
        if self.speed <= 0:
            return "∞"

        remaining_bytes = self.file_size - self.downloaded_size
        seconds = remaining_bytes / self.speed

        if seconds < 60:
            return f"{seconds:.0f} sec"
        elif seconds < 3600:
            return f"{seconds / 60:.1f} min"
        else:
            return f"{seconds / 3600:.1f} hours"

class DownloadManager(EventDispatcher):
    """Manager for handling multiple downloads"""
    downloads = ListProperty([])
    active_downloads = NumericProperty(0)
    max_concurrent_downloads = NumericProperty(3)
    download_path = StringProperty("")
    download_history = ListProperty([])
    history_file = StringProperty("")

    def __init__(self, download_path="", **kwargs):
        super(DownloadManager, self).__init__(**kwargs)

        # Set default download path if not provided
        if not download_path:
            from kivy.utils import platform
            if platform == 'android':
                from android.storage import app_storage_path
                download_path = os.path.join(app_storage_path(), "downloads")
            else:
                download_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "downloads")

        self.download_path = download_path

        # Create download directory if it doesn't exist
        if not os.path.exists(self.download_path):
            try:
                os.makedirs(self.download_path)
                logger.info(f"Created download directory: {self.download_path}")
            except Exception as e:
                logger.error(f"Failed to create download directory: {e}")

        # Set history file path
        self.history_file = os.path.join(self.download_path, "download_history.json")

        # Load download history
        self.load_download_history()

        # Start download processor
        Clock.schedule_interval(self.process_queue, 1)

    def add_download(self, url):
        """Add a new download to the queue"""
        # Basic URL validation
        if not url.startswith(('http://', 'https://')):
            logger.error(f"Invalid URL: {url}")
            return False, None

        # Create download item
        download_item = DownloadItem(url=url, download_path=self.download_path)

        # If it's a YouTube URL and we have a YouTube download library available
        if download_item.is_youtube and YOUTUBE_DOWNLOAD_AVAILABLE:
            # Add to downloads list first so it shows up in UI
            self.downloads.append(download_item)
            logger.info(f"Added YouTube download: {url}")

            # Fetch formats in a separate thread to avoid blocking UI
            threading.Thread(
                target=self._fetch_youtube_formats_thread,
                args=(download_item,)
            ).start()

            # Return True and the download item for further processing
            return True, download_item
        elif download_item.is_youtube and not YOUTUBE_DOWNLOAD_AVAILABLE:
            # It's a YouTube URL but we don't have a library to handle it
            logger.error(f"YouTube URL detected but no YouTube download library available: {url}")
            return False, None
        else:
            # For regular downloads, just add to queue
            self.downloads.append(download_item)
            logger.info(f"Added download: {url}")
            return True, download_item

    def _fetch_youtube_formats_thread(self, download_item):
        """Fetch YouTube formats in a separate thread"""
        try:
            formats = download_item.fetch_youtube_formats()
            logger.info(f"Fetched {len(formats)} formats for {download_item.url}")
        except Exception as e:
            logger.error(f"Error fetching YouTube formats: {e}")
            download_item.error_message = f"Error fetching formats: {str(e)}"
            download_item.status = "error"

    def process_queue(self, dt):
        """Process the download queue"""
        # Count active downloads
        self.active_downloads = len([d for d in self.downloads if d.status == "downloading"])

        # Start new downloads if under the limit
        if self.active_downloads < self.max_concurrent_downloads:
            pending_downloads = [d for d in self.downloads if d.status == "pending"]

            for download in pending_downloads:
                if self.active_downloads >= self.max_concurrent_downloads:
                    break

                # Start this download
                self._start_download(download)
                self.active_downloads += 1

    def _start_download(self, download_item):
        """Start downloading a specific item"""
        # If it's a YouTube URL and no format is selected, don't start download
        if download_item.is_youtube and YOUTUBE_DOWNLOAD_AVAILABLE and not download_item.selected_format:
            logger.info(f"YouTube download {download_item.url} waiting for format selection")
            return

        download_item.status = "downloading"
        download_item.start_time = time.time()

        # Start download in a separate thread
        thread = threading.Thread(
            target=self._download_thread,
            args=(download_item,)
        )
        thread.daemon = True
        thread.start()

    def _download_thread(self, download_item):
        """Thread function for downloading a file"""
        try:
            # Handle YouTube downloads differently
            if download_item.is_youtube and YOUTUBE_DOWNLOAD_AVAILABLE and download_item.selected_format:
                self._download_youtube_video(download_item)
                return

            # Regular download process for non-YouTube URLs
            # Make a HEAD request to get file size
            head_response = requests.head(download_item.url, timeout=10)
            file_size = int(head_response.headers.get('content-length', 0))
            download_item.file_size = file_size

            # Start the actual download
            response = requests.get(download_item.url, stream=True, timeout=30)
            response.raise_for_status()

            # Create the file
            with open(download_item.full_path, 'wb') as f:
                downloaded_size = 0
                start_time = time.time()
                last_update_time = start_time
                chunk_size = 8192

                for chunk in response.iter_content(chunk_size=chunk_size):
                    if download_item.status == "canceled":
                        # Delete the partial file
                        f.close()
                        if os.path.exists(download_item.full_path):
                            os.remove(download_item.full_path)
                        return

                    if download_item.status == "paused":
                        # Wait until unpaused
                        while download_item.status == "paused":
                            time.sleep(0.5)

                        # If canceled while paused
                        if download_item.status == "canceled":
                            f.close()
                            if os.path.exists(download_item.full_path):
                                os.remove(download_item.full_path)
                            return

                    # Write chunk to file
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # Update progress
                        current_time = time.time()
                        if current_time - last_update_time >= 0.5:  # Update every 0.5 seconds
                            self._update_progress(
                                download_item,
                                downloaded_size,
                                file_size,
                                downloaded_size / (current_time - start_time)
                            )
                            last_update_time = current_time

            # Final update
            self._update_progress(download_item, downloaded_size, file_size,
                                 downloaded_size / (time.time() - start_time))
            self._complete_download(download_item)

        except requests.exceptions.RequestException as e:
            self._fail_download(download_item, str(e))
        except Exception as e:
            self._fail_download(download_item, f"Download error: {str(e)}")

    def _download_youtube_video(self, download_item):
        """Download a YouTube video with the selected format"""
        # Try using yt-dlp first (more reliable)
        if YTDLP_AVAILABLE:
            try:
                self._download_with_ytdlp(download_item)
                return
            except Exception as e:
                logger.warning(f"Error downloading with yt-dlp: {e}. Trying pytube.")
                # If yt-dlp fails, try pytube if available
                if PYTUBE_AVAILABLE:
                    try:
                        self._download_with_pytube(download_item)
                        return
                    except Exception as e2:
                        logger.error(f"Error downloading with pytube: {e2}")
                        self._fail_download(download_item, f"Download failed with both yt-dlp and pytube: {str(e2)}")
                else:
                    self._fail_download(download_item, f"yt-dlp download failed and pytube not available: {str(e)}")
        # If yt-dlp is not available, try pytube
        elif PYTUBE_AVAILABLE:
            try:
                self._download_with_pytube(download_item)
                return
            except Exception as e:
                logger.error(f"Error downloading with pytube: {e}")
                self._fail_download(download_item, f"Pytube download failed: {str(e)}")
        else:
            # Neither library is available
            self._fail_download(download_item, "No YouTube download library available")

    def _download_with_ytdlp(self, download_item):
        """Download YouTube video using yt-dlp"""
        try:
            selected_format = download_item.selected_format

            # Make sure download directory exists
            os.makedirs(download_item.download_path, exist_ok=True)

            # Prepare filename
            if not download_item.filename.endswith(f".{selected_format.extension}"):
                base_name = os.path.splitext(download_item.filename)[0]
                download_item.filename = f"{base_name}.{selected_format.extension}"
                download_item.full_path = os.path.join(download_item.download_path, download_item.filename)

            # Custom progress hook for yt-dlp
            def progress_hook(d):
                if download_item.status == "canceled":
                    raise Exception("Download canceled")

                if download_item.status == "paused":
                    # Wait until unpaused
                    while download_item.status == "paused":
                        time.sleep(0.5)

                    # If canceled while paused
                    if download_item.status == "canceled":
                        raise Exception("Download canceled")

                if d['status'] == 'downloading':
                    # Get downloaded bytes and total bytes
                    downloaded = d.get('downloaded_bytes', 0)
                    total = d.get('total_bytes', 0) or d.get('total_bytes_estimate', 0)

                    # Update file size if we have it
                    if total > 0 and download_item.file_size == 0:
                        download_item.file_size = total

                    # Calculate speed
                    speed = d.get('speed', 0) or 0

                    # Update progress
                    if total > 0:
                        self._update_progress(download_item, downloaded, total, speed)

            # Configure yt-dlp options
            ydl_opts = {
                'progress_hooks': [progress_hook],
                'quiet': True,
                'no_warnings': True,
                'noplaylist': True,
                'outtmpl': download_item.full_path,
                'writethumbnail': True,  # Download thumbnail
                'embedthumbnail': True,  # Embed thumbnail in the audio file
            }

            # Special handling for MP3 format (our custom format with itag 9999)
            if selected_format.itag == 9999:
                # Configure for MP3 extraction
                ydl_opts.update({
                    'format': 'bestaudio/best',
                    'postprocessors': [
                        {
                            'key': 'FFmpegExtractAudio',
                            'preferredcodec': 'mp3',
                            'preferredquality': '192',
                        },
                        {
                            'key': 'EmbedThumbnail',  # Embed thumbnail in the audio file
                        },
                    ],
                })
            # Special handling for WAV format (our custom format with itag 9998)
            elif selected_format.itag == 9998:
                # Configure for WAV extraction
                ydl_opts.update({
                    'format': 'bestaudio/best',
                    'postprocessors': [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': 'wav',
                        'preferredquality': '192',
                    }],
                    # Note: WAV doesn't support embedded thumbnails, so we'll save it separately
                })
            else:
                # Regular format selection
                format_id = str(selected_format.itag)
                ydl_opts['format'] = format_id

            # Download the video and thumbnail
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(download_item.url, download=False)
                thumbnail_url = info.get('thumbnail')

                # Save the thumbnail URL for later use
                download_item.thumbnail_url = thumbnail_url

                # Download the video
                ydl.download([download_item.url])

                # For WAV files, we need to save the thumbnail separately
                if selected_format.itag == 9998 and thumbnail_url:
                    try:
                        # Create album_covers directory if it doesn't exist
                        from kivy.app import App
                        app = App.get_running_app()
                        if hasattr(app, 'root') and hasattr(app.root, 'get_app_data_dir'):
                            covers_dir = os.path.join(app.root.get_app_data_dir(), 'album_covers')
                        else:
                            covers_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers')

                        if not os.path.exists(covers_dir):
                            os.makedirs(covers_dir, exist_ok=True)

                        # Generate a unique filename for the cover
                        import hashlib
                        file_hash = hashlib.md5(download_item.full_path.encode()).hexdigest()
                        cover_path = os.path.join(covers_dir, f"cover_{file_hash}.jpg")

                        # Download the thumbnail
                        response = requests.get(thumbnail_url, timeout=30)
                        if response.status_code == 200:
                            with open(cover_path, 'wb') as f:
                                f.write(response.content)
                            logger.info(f"Downloaded album cover for WAV file: {cover_path}")
                    except Exception as thumb_error:
                        logger.error(f"Error downloading thumbnail for WAV file: {thumb_error}")

            # Mark as complete
            self._complete_download(download_item)

        except Exception as e:
            if download_item.status == "canceled":
                # If the download was canceled, delete the partial file
                if os.path.exists(download_item.full_path):
                    try:
                        os.remove(download_item.full_path)
                    except:
                        pass
                return

            logger.error(f"Error in _download_with_ytdlp: {e}")
            raise

    def _download_with_pytube(self, download_item):
        """Download YouTube video using pytube"""
        try:
            # Create YouTube object with additional options for better error handling
            yt = YouTube(
                download_item.url,
                use_oauth=False,
                allow_oauth_cache=True,
                on_progress_callback=None,
                on_complete_callback=None,
                proxies=None
            )

            # Get the selected stream by itag
            selected_format = download_item.selected_format
            stream = None

            # Special handling for MP3 format (our custom format with itag 9999)
            if selected_format.itag == 9999:
                # For MP3, we'll download the best audio stream and convert it later
                stream = yt.streams.filter(only_audio=True).order_by('abr').desc().first()

                # If no audio stream is found, try to get any stream
                if not stream:
                    stream = yt.streams.first()

                # We'll need to convert this to MP3 after download
                download_item.needs_mp3_conversion = True
                download_item.target_format = 'mp3'
            # Special handling for WAV format (our custom format with itag 9998)
            elif selected_format.itag == 9998:
                # For WAV, we'll download the best audio stream and convert it later
                stream = yt.streams.filter(only_audio=True).order_by('abr').desc().first()

                # If no audio stream is found, try to get any stream
                if not stream:
                    stream = yt.streams.first()

                # We'll need to convert this to WAV after download
                download_item.needs_mp3_conversion = True  # Reusing the same flag
                download_item.target_format = 'wav'
            else:
                # Regular format selection
                try:
                    stream = yt.streams.get_by_itag(selected_format.itag)
                except Exception as stream_error:
                    logger.warning(f"Error getting stream by itag: {stream_error}")
                    # Try to get a fallback stream
                    if selected_format.is_audio_only:
                        stream = yt.streams.filter(only_audio=True).first()
                    elif selected_format.is_video_only:
                        stream = yt.streams.filter(only_video=True).first()
                    else:
                        stream = yt.streams.filter(progressive=True).first()

            if not stream:
                # Last resort fallback - try to get any stream
                stream = yt.streams.first()

            if not stream:
                raise Exception("Selected format not available and no fallback found")

            # Update file size
            try:
                download_item.file_size = stream.filesize
            except:
                download_item.file_size = 0

            # Set up progress callback
            def progress_callback(stream, chunk, bytes_remaining):
                if download_item.status == "canceled":
                    # This will be caught in the exception handler
                    raise Exception("Download canceled")

                if download_item.status == "paused":
                    # Wait until unpaused
                    while download_item.status == "paused":
                        time.sleep(0.5)

                    # If canceled while paused
                    if download_item.status == "canceled":
                        raise Exception("Download canceled")

                # Calculate progress
                if download_item.file_size > 0:
                    bytes_downloaded = download_item.file_size - bytes_remaining
                    progress = int(100 * bytes_downloaded / download_item.file_size)

                    # Calculate speed
                    current_time = time.time()
                    elapsed_time = current_time - download_item.start_time
                    if elapsed_time > 0:
                        speed = bytes_downloaded / elapsed_time
                    else:
                        speed = 0

                    # Update progress
                    self._update_progress(download_item, bytes_downloaded, download_item.file_size, speed)

            # Set up progress callback
            try:
                yt.register_on_progress_callback(progress_callback)
            except Exception as callback_error:
                logger.warning(f"Error registering progress callback: {callback_error}")

            # Start download
            # If the extension is different from the original filename, update it
            try:
                extension = stream.subtype
                if extension and not download_item.filename.endswith(f".{extension}"):
                    base_name = os.path.splitext(download_item.filename)[0]
                    download_item.filename = f"{base_name}.{extension}"
                    download_item.full_path = os.path.join(download_item.download_path, download_item.filename)
            except Exception as ext_error:
                logger.warning(f"Error updating filename extension: {ext_error}")

            # Make sure download directory exists
            os.makedirs(os.path.dirname(download_item.full_path), exist_ok=True)

            # Download the file
            try:
                stream.download(
                    output_path=download_item.download_path,
                    filename=download_item.filename
                )

                # If audio conversion is needed
                if download_item.needs_mp3_conversion:
                    # Get the target format (default to mp3 if not specified)
                    target_format = getattr(download_item, 'target_format', 'mp3')

                    # Update status to indicate conversion
                    download_item.status = "converting"
                    self._update_progress(download_item, 100, 100, 0)  # Show 100% for download

                    logger.info(f"Converting downloaded file to {target_format} format")

                    try:
                        # Get the downloaded file path
                        downloaded_file = download_item.full_path

                        # Create the output file path
                        base_name = os.path.splitext(downloaded_file)[0]
                        output_file = f"{base_name}.{target_format}"

                        # Try to use FFmpeg for conversion if available
                        try:
                            import subprocess

                            # Base command for conversion
                            cmd = [
                                'ffmpeg',
                                '-i', downloaded_file,
                                '-vn',  # No video
                                '-ar', '44100',  # Audio sample rate
                                '-ac', '2',  # Stereo
                            ]

                            # Add format-specific options
                            if target_format == 'mp3':
                                cmd.extend([
                                    '-b:a', '192k',  # Bitrate for MP3
                                    '-f', 'mp3',     # Format
                                ])
                            elif target_format == 'wav':
                                cmd.extend([
                                    '-acodec', 'pcm_s16le',  # WAV codec
                                    '-f', 'wav',             # Format
                                ])

                            # Add output file
                            cmd.append(output_file)

                            logger.info(f"Running FFmpeg command: {' '.join(cmd)}")

                            # Run the conversion
                            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                            # Remove the original file if conversion succeeded
                            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                                logger.info(f"Conversion successful. Output file: {output_file}")
                                os.remove(downloaded_file)

                                # Update the file path
                                download_item.filename = os.path.basename(output_file)
                                download_item.full_path = output_file
                            else:
                                logger.warning(f"Converted file not found or empty: {output_file}")

                        except (ImportError, subprocess.SubprocessError, FileNotFoundError) as e:
                            logger.warning(f"FFmpeg conversion failed: {e}. {target_format.upper()} conversion not available.")
                            # Keep the original file if conversion fails
                            pass

                    except Exception as conv_error:
                        logger.warning(f"Error during {target_format.upper()} conversion: {conv_error}")
                        import traceback
                        logger.error(traceback.format_exc())
                        # Continue with the original file if conversion fails

                # Mark as complete
                self._complete_download(download_item)
            except Exception as download_error:
                # Try alternative download method
                logger.warning(f"Error in primary download method: {download_error}. Trying alternative method.")
                try:
                    # Alternative download method using requests
                    response = requests.get(stream.url, stream=True, timeout=30)
                    response.raise_for_status()

                    with open(download_item.full_path, 'wb') as f:
                        downloaded_size = 0
                        start_time = time.time()
                        last_update_time = start_time
                        chunk_size = 8192

                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if download_item.status == "canceled":
                                # Delete the partial file
                                f.close()
                                if os.path.exists(download_item.full_path):
                                    os.remove(download_item.full_path)
                                return

                            if download_item.status == "paused":
                                # Wait until unpaused
                                while download_item.status == "paused":
                                    time.sleep(0.5)

                                # If canceled while paused
                                if download_item.status == "canceled":
                                    f.close()
                                    if os.path.exists(download_item.full_path):
                                        os.remove(download_item.full_path)
                                    return

                            # Write chunk to file
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)

                                # Update progress
                                current_time = time.time()
                                if current_time - last_update_time >= 0.5:  # Update every 0.5 seconds
                                    self._update_progress(
                                        download_item,
                                        downloaded_size,
                                        download_item.file_size,
                                        downloaded_size / (current_time - start_time)
                                    )
                                    last_update_time = current_time

                    # If audio conversion is needed for the alternative method
                    if download_item.needs_mp3_conversion:
                        # Get the target format (default to mp3 if not specified)
                        target_format = getattr(download_item, 'target_format', 'mp3')

                        # Update status to indicate conversion
                        download_item.status = "converting"
                        self._update_progress(download_item, 100, 100, 0)

                        logger.info(f"Converting downloaded file to {target_format} format (alternative method)")

                        try:
                            # Get the downloaded file path
                            downloaded_file = download_item.full_path

                            # Create the output file path
                            base_name = os.path.splitext(downloaded_file)[0]
                            output_file = f"{base_name}.{target_format}"

                            # Try to use FFmpeg for conversion if available
                            try:
                                import subprocess

                                # Base command for conversion
                                cmd = [
                                    'ffmpeg',
                                    '-i', downloaded_file,
                                    '-vn',  # No video
                                    '-ar', '44100',  # Audio sample rate
                                    '-ac', '2',  # Stereo
                                ]

                                # Add format-specific options
                                if target_format == 'mp3':
                                    cmd.extend([
                                        '-b:a', '192k',  # Bitrate for MP3
                                        '-f', 'mp3',     # Format
                                    ])
                                elif target_format == 'wav':
                                    cmd.extend([
                                        '-acodec', 'pcm_s16le',  # WAV codec
                                        '-f', 'wav',             # Format
                                    ])

                                # Add output file
                                cmd.append(output_file)

                                logger.info(f"Running FFmpeg command: {' '.join(cmd)}")

                                # Run the conversion
                                subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                                # Remove the original file if conversion succeeded
                                if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                                    logger.info(f"Conversion successful. Output file: {output_file}")
                                    os.remove(downloaded_file)

                                    # Update the file path
                                    download_item.filename = os.path.basename(output_file)
                                    download_item.full_path = output_file
                                else:
                                    logger.warning(f"Converted file not found or empty: {output_file}")

                            except (ImportError, subprocess.SubprocessError, FileNotFoundError) as e:
                                logger.warning(f"FFmpeg conversion failed: {e}. {target_format.upper()} conversion not available.")
                                # Keep the original file if conversion fails
                                pass

                        except Exception as conv_error:
                            logger.warning(f"Error during {target_format.upper()} conversion: {conv_error}")
                            import traceback
                            logger.error(traceback.format_exc())
                            # Continue with the original file if conversion fails

                    # Mark as complete
                    self._complete_download(download_item)
                except Exception as alt_error:
                    raise Exception(f"Alternative download method failed: {str(alt_error)}")

        except Exception as e:
            if download_item.status == "canceled":
                # If the download was canceled, delete the partial file
                if os.path.exists(download_item.full_path):
                    try:
                        os.remove(download_item.full_path)
                    except:
                        pass
                return

            logger.error(f"Error in _download_with_pytube: {e}")
            raise

    @mainthread
    def _update_progress(self, download_item, downloaded_size, file_size, speed):
        """Update download progress (called from thread)"""
        download_item.downloaded_size = downloaded_size
        if file_size > 0:
            download_item.progress = int(100 * downloaded_size / file_size)
        download_item.speed = speed

    @mainthread
    def _complete_download(self, download_item):
        """Mark download as complete (called from thread)"""
        download_item.status = "completed"
        download_item.progress = 100
        logger.info(f"Download completed: {download_item.filename}")

        try:
            # Double check if the file exists and is accessible
            if not os.path.isfile(download_item.full_path):
                logger.error(f"Downloaded file does not exist: {download_item.full_path}")

                # Try to find the file with a similar name in the download directory
                download_dir = os.path.dirname(download_item.full_path)
                base_name = os.path.splitext(os.path.basename(download_item.full_path))[0]
                ext = os.path.splitext(download_item.full_path)[1]

                logger.info(f"Looking for similar files in {download_dir} with base name: {base_name}")

                # List all files in the download directory
                if os.path.exists(download_dir):
                    files = os.listdir(download_dir)
                    potential_matches = []

                    for file in files:
                        # Check if this file has the same extension and similar name
                        if file.endswith(ext) and (base_name.lower() in file.lower() or
                                                  file.lower() in base_name.lower()):
                            potential_matches.append(file)

                    if potential_matches:
                        # Use the first match
                        match = potential_matches[0]
                        logger.info(f"Found potential match: {match}")
                        download_item.filename = match
                        download_item.full_path = os.path.join(download_dir, match)

                        if not os.path.isfile(download_item.full_path):
                            logger.error(f"Matched file still does not exist: {download_item.full_path}")
                            return
                    else:
                        logger.error(f"No similar files found in {download_dir}")
                        return
                else:
                    logger.error(f"Download directory does not exist: {download_dir}")
                    return

            # Verify file size
            file_size = os.path.getsize(download_item.full_path)
            if file_size == 0:
                logger.error(f"Downloaded file is empty: {download_item.full_path}")
                return

            # Update file size and download date
            download_item.file_size = file_size
            download_item.download_date = time.strftime('%Y-%m-%d %H:%M:%S')

            logger.info(f"Downloaded file exists at: {download_item.full_path} (Size: {file_size} bytes)")

            # Add to download history
            self.add_to_history(download_item)

            # Save history to file immediately
            self.save_download_history()

            # Show a toast notification
            try:
                from kivymd.toast import toast
                toast(f"تم تنزيل {download_item.filename} بنجاح وإضافته إلى السجل")
            except Exception as toast_error:
                logger.error(f"Error showing toast: {toast_error}")

            # Check if the downloaded file is an audio file
            file_ext = os.path.splitext(download_item.filename)[1].lower()
            audio_extensions = ['.mp3', '.wav', '.flac', '.m4a', '.mp4', '.aac', '.ogg', '.opus', '.wma', '.asf', '.aiff', '.aif', '.aifc']

            logger.info(f"File extension: {file_ext}, Supported extensions: {audio_extensions}")

            if file_ext in audio_extensions:
                logger.info(f"File is a supported audio format: {file_ext}")

                # Add the downloaded audio file to the music library
                try:
                    from kivy.app import App
                    from kivy.clock import Clock
                    app = App.get_running_app()
                    logger.info(f"App: {app}, Has root: {hasattr(app, 'root')}")

                    if hasattr(app, 'root'):
                        logger.info(f"Root has add_to_playlist_at_beginning: {hasattr(app.root, 'add_to_playlist_at_beginning')}")

                    if hasattr(app, 'root') and hasattr(app.root, 'add_to_playlist_at_beginning'):
                        # Make sure the path is absolute and exists
                        full_path = os.path.abspath(download_item.full_path)

                        if not os.path.isfile(full_path):
                            logger.error(f"Absolute path does not exist: {full_path}")
                            return

                        logger.info(f"Adding file to playlist: {full_path}")

                        # Call the method directly on the main thread
                        app.root.add_to_playlist_at_beginning([full_path])
                        logger.info(f"Added downloaded file to music library: {download_item.filename}")

                        # Force UI update after a short delay to ensure the playlist is updated
                        def force_ui_update(dt):
                            if hasattr(app.root, 'update_playlist_ui'):
                                logger.info("Forcing playlist UI update")
                                app.root.update_playlist_ui()

                                # Show a toast notification
                                try:
                                    from kivymd.toast import toast
                                    toast(f"Added {download_item.filename} to playlist")
                                except Exception as toast_error:
                                    logger.error(f"Error showing toast: {toast_error}")

                                # Try to extract album cover and download it if not found
                                if hasattr(app.root, 'extract_album_cover'):
                                    try:
                                        logger.info(f"Extracting album cover for: {full_path}")
                                        # Extract metadata to use for online search if needed
                                        artist = None
                                        title = None
                                        album = None

                                        try:
                                            # Try to get metadata from the file
                                            import mutagen
                                            audio = mutagen.File(full_path)
                                            if audio:
                                                # Extract metadata based on file type
                                                if hasattr(audio, 'tags') and audio.tags:
                                                    # MP3 files
                                                    if 'TPE1' in audio.tags:
                                                        artist = str(audio.tags['TPE1'])
                                                    if 'TIT2' in audio.tags:
                                                        title = str(audio.tags['TIT2'])
                                                    if 'TALB' in audio.tags:
                                                        album = str(audio.tags['TALB'])
                                                elif hasattr(audio, 'get'):
                                                    # FLAC, OGG, etc.
                                                    artist = audio.get('artist', [''])[0]
                                                    title = audio.get('title', [''])[0]
                                                    album = audio.get('album', [''])[0]

                                            # If metadata extraction failed, try to parse from filename
                                            if not (artist or title):
                                                filename = os.path.basename(full_path)
                                                filename_without_ext = os.path.splitext(filename)[0]

                                                # Try to split by common separators
                                                if ' - ' in filename_without_ext:
                                                    parts = filename_without_ext.split(' - ', 1)
                                                    artist = parts[0].strip()
                                                    title = parts[1].strip()
                                                elif '-' in filename_without_ext:
                                                    parts = filename_without_ext.split('-', 1)
                                                    artist = parts[0].strip()
                                                    title = parts[1].strip()
                                        except Exception as metadata_error:
                                            logger.warning(f"Error extracting metadata: {metadata_error}")

                                        # First try to extract cover from the file
                                        cover_data = app.root.extract_album_cover(full_path)

                                        # Usar solo la portada extraída del archivo, no buscar en línea
                                        # If we have cover data, update the UI
                                        if cover_data and hasattr(app.root, 'update_album_cover'):
                                                try:
                                                    # Schedule the update on the main thread
                                                    def update_cover(dt):
                                                        app.root.update_album_cover(full_path)
                                                    Clock.schedule_once(update_cover, 2.0)
                                                    logger.info("Scheduled album cover update for embedded cover")
                                                except Exception as update_error:
                                                    logger.error(f"Error scheduling album cover update: {update_error}")
                                    except Exception as cover_error:
                                        logger.error(f"Error processing album cover: {cover_error}")

                        # Schedule the UI update after a short delay
                        Clock.schedule_once(force_ui_update, 1.0)
                    else:
                        logger.error("App.root or add_to_playlist_at_beginning method not found")
                except Exception as e:
                    logger.error(f"Error adding file to playlist: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
        except Exception as e:
            logger.error(f"Error in _complete_download: {e}")
            import traceback
            logger.error(traceback.format_exc())

    @mainthread
    def _fail_download(self, download_item, error_message):
        """Mark download as failed (called from thread)"""
        download_item.status = "error"
        download_item.error_message = error_message
        logger.error(f"Download failed: {download_item.filename} - {error_message}")

    def pause_download(self, download_item):
        """Pause a download"""
        if download_item.status == "downloading":
            download_item.status = "paused"
            logger.info(f"Download paused: {download_item.filename}")

    def resume_download(self, download_item):
        """Resume a paused download"""
        if download_item.status == "paused":
            download_item.status = "pending"
            logger.info(f"Download resumed: {download_item.filename}")

    def cancel_download(self, download_item):
        """Cancel a download"""
        if download_item.status in ["downloading", "paused", "pending"]:
            download_item.status = "canceled"
            logger.info(f"Download canceled: {download_item.filename}")

    def retry_download(self, download_item):
        """Retry a failed download"""
        if download_item.status == "error":
            download_item.status = "pending"
            download_item.progress = 0
            download_item.error_message = ""
            logger.info(f"Retrying download: {download_item.filename}")

    def remove_download(self, download_item):
        """Remove a download from the list"""
        if download_item in self.downloads:
            if download_item.status == "downloading":
                self.cancel_download(download_item)
            self.downloads.remove(download_item)
            logger.info(f"Download removed: {download_item.filename}")

    def set_download_format(self, download_item, format_itag):
        """Set the selected format for a YouTube download"""
        if not download_item.is_youtube or not PYTUBE_AVAILABLE:
            return False

        # Find the format with the given itag
        selected_format = None
        for format_obj in download_item.available_formats:
            if format_obj.itag == format_itag:
                selected_format = format_obj
                break

        if not selected_format:
            logger.error(f"Format with itag {format_itag} not found")
            return False

        # Set the selected format
        download_item.selected_format = selected_format

        # Update filename extension if needed
        if selected_format.extension:
            base_name = os.path.splitext(download_item.filename)[0]
            download_item.filename = f"{base_name}.{selected_format.extension}"
            download_item.full_path = os.path.join(download_item.download_path, download_item.filename)

        logger.info(f"Selected format {format_itag} for {download_item.url}")

        # If the download is pending, start it
        if download_item.status == "pending":
            self._start_download(download_item)

        return True

    def clear_completed(self):
        """Clear all completed downloads from the list"""
        completed_downloads = [d for d in self.downloads if d.status == "completed"]
        for download in completed_downloads:
            # Add to history before removing
            self.add_to_history(download)
            self.downloads.remove(download)
        logger.info(f"Cleared {len(completed_downloads)} completed downloads")

    def add_to_history(self, download_item):
        """Add a completed download to the history"""
        try:
            # Only add completed downloads to history
            if download_item.status != "completed":
                return

            # Use download_date from item if available, otherwise use current time
            download_date = time.strftime('%Y-%m-%d %H:%M:%S')
            if hasattr(download_item, 'download_date') and download_item.download_date:
                download_date = download_item.download_date

            # Get file extension
            file_ext = os.path.splitext(download_item.filename)[1].lower()

            # Create a history entry
            history_entry = {
                'url': download_item.url,
                'filename': download_item.filename,
                'full_path': download_item.full_path,
                'download_date': download_date,
                'file_size': download_item.file_size,
                'is_youtube': download_item.is_youtube,
                'file_type': file_ext[1:] if file_ext.startswith('.') else file_ext  # Store without the dot
            }

            # Check if this item already exists in history
            exists = False
            for entry in self.download_history:
                if entry.get('url') == download_item.url and entry.get('filename') == download_item.filename:
                    exists = True
                    # Update the existing entry with new information
                    entry.update(history_entry)
                    logger.info(f"Updated existing history entry: {download_item.filename}")
                    break

            # Only add if it doesn't already exist
            if not exists:
                # Add to history list
                self.download_history.append(history_entry)
                logger.info(f"Added to download history: {download_item.filename}")

            # Save history to file
            self.save_download_history()

        except Exception as e:
            logger.error(f"Error adding to download history: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def save_download_history(self):
        """Save download history to a JSON file"""
        try:
            # Make sure the download directory exists
            history_dir = os.path.dirname(self.history_file)
            if not os.path.exists(history_dir):
                os.makedirs(history_dir, exist_ok=True)
                logger.info(f"Created history directory: {history_dir}")

            # Check if we have write permission
            if not os.access(history_dir, os.W_OK):
                logger.error(f"No write permission for directory: {history_dir}")

                # Try to use a different location
                from kivy.app import App
                app = App.get_running_app()
                if hasattr(app, 'root') and hasattr(app.root, 'get_app_data_dir'):
                    alt_dir = os.path.join(app.root.get_app_data_dir(), 'downloads')
                    if not os.path.exists(alt_dir):
                        os.makedirs(alt_dir, exist_ok=True)

                    self.history_file = os.path.join(alt_dir, "download_history.json")
                    logger.info(f"Using alternative history file location: {self.history_file}")

            # Save history to file
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.download_history, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved download history to {self.history_file}")
        except Exception as e:
            logger.error(f"Error saving download history: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def load_download_history(self):
        """Load download history from a JSON file"""
        try:
            if os.path.exists(self.history_file) and os.path.getsize(self.history_file) > 0:
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        self.download_history = json.load(f)
                    logger.info(f"Loaded {len(self.download_history)} entries from download history")
                except json.JSONDecodeError:
                    # El archivo existe pero no es un JSON válido
                    logger.warning(f"Invalid JSON in history file: {self.history_file}")
                    self.download_history = []
                    # Crear un archivo JSON válido
                    self.save_download_history()
            else:
                self.download_history = []
                logger.info("No download history file found or file is empty, starting with empty history")
                # Crear un archivo JSON válido
                self.save_download_history()
        except Exception as e:
            logger.error(f"Error loading download history: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.download_history = []
            # Intentar crear un archivo JSON válido
            try:
                self.save_download_history()
            except:
                pass

    def remove_from_history(self, history_item):
        """Remove a specific item from download history"""
        try:
            # Find and remove the item from history
            for i, item in enumerate(self.download_history):
                if (item.get('url') == history_item.get('url') and
                    item.get('filename') == history_item.get('filename')):
                    self.download_history.pop(i)
                    logger.info(f"Removed item from download history: {history_item.get('filename')}")
                    break

            # Save the updated history
            self.save_download_history()
            return True
        except Exception as e:
            logger.error(f"Error removing item from download history: {e}")
            return False
