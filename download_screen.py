"""
Download Screen for Music Player Application
Provides UI for downloading music from URLs and monitoring download progress
"""

from kivy.uix.screenmanager import Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.properties import ObjectProperty, StringProperty, NumericProperty, BooleanProperty
from kivy.metrics import sp, dp  # Importar sp y dp para tamaños de fuente y dimensiones
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton, MDRaisedButton, MDIconButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.progressbar import MDProgressBar
from kivymd.uix.label import MDLabel
from kivymd.uix.list import OneLineIconListItem, IconLeftWidget, OneLineListItem, MDList, TwoLineAvatarIconListItem
from kivymd.app import MDApp
from kivymd.uix.spinner import MDSpinner
from kivymd.uix.tab import MDTabsBase
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.snackbar import Snackbar
import os
import logging
import datetime

# Import Arabic text handling utilities
try:
    from arabic_utils import reshape_arabic_text, contains_arabic, get_display_text, ARABIC_SUPPORT
    logger = logging.getLogger(__name__)
    logger.info("Arabic text utilities imported successfully in download screen")
except ImportError as e:
    logger.warning(f"Failed to import Arabic text utilities in download screen: {e}")
    # Define fallback functions if the module is not available
    def contains_arabic(text): return False
    def reshape_arabic_text(text): return text
    def get_display_text(text, always_process=False): return text
    ARABIC_SUPPORT = False

# Configure logging
logger = logging.getLogger(__name__)

class Tab(MDFloatLayout, MDTabsBase):
    """Base class for tabs"""
    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class ActiveDownloadsTab(Tab):
    """Tab for active downloads"""
    download_screen = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(ActiveDownloadsTab, self).__init__(**kwargs)
        # Add icon to the tab title
        self.title = "  Downloads"  # Add space for the icon

        # We'll add the icon in the on_enter method of DownloadScreen

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class HistoryTab(Tab):
    """Tab for download history"""
    download_screen = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(HistoryTab, self).__init__(**kwargs)
        # Add icon to the tab title
        self.title = "  History"  # Add space for the icon

        # We'll add the icon in the on_enter method of DownloadScreen

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class DownloadScreen(Screen):
    """Screen for downloading music and monitoring downloads"""
    download_manager = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(DownloadScreen, self).__init__(**kwargs)
        self.name = 'downloads'
        self.url_dialog = None
        self.format_dialog = None
        self.current_youtube_item = None

        # Schedule periodic updates for YouTube format fetching
        from kivy.clock import Clock
        Clock.schedule_interval(self.check_youtube_formats, 1)

    def on_enter(self):
        """Called when the screen is entered"""
        # Update the download list and history
        self.update_download_list()
        self.update_history_list()

        # Fix tab titles with icons
        try:
            # We need to update the tab text after the screen is created
            # This is a workaround since KivyMD tabs don't directly support icons
            if hasattr(self.ids, 'tabs'):
                tabs = self.ids.tabs
                # Find the tab instances
                for tab in tabs.get_tab_list():
                    if isinstance(tab, ActiveDownloadsTab):
                        tab.title = "  Downloads"  # Space for icon
                        # Add download icon to the tab slide
                        for slide in tabs.get_slides():
                            if slide == tab:
                                # Find the tab label and add an icon
                                for child in tabs.ids.scrollview.children[0].children:
                                    if hasattr(child, 'text') and "Downloads" in child.text:
                                        child.text = "⬇️ Downloads"  # Using emoji as icon
                    elif isinstance(tab, HistoryTab):
                        tab.title = "  History"  # Space for icon
                        # Add history icon to the tab slide
                        for slide in tabs.get_slides():
                            if slide == tab:
                                # Find the tab label and add an icon
                                for child in tabs.ids.scrollview.children[0].children:
                                    if hasattr(child, 'text') and "History" in child.text:
                                        child.text = "🕒 History"  # Using emoji as icon
        except Exception as e:
            logger.error(f"Error setting tab icons: {e}")

    def on_tabs_changed(self, instance_tabs, instance_tab, instance_tab_label, tab_text):
        """Handle tab change events"""
        # Check for both the original and emoji-prefixed tab names
        if "Downloads" in tab_text:
            self.update_download_list()
        elif "History" in tab_text:
            self.update_history_list()

    def update_download_list(self):
        """Update the download list UI"""
        # Clear the current list
        if hasattr(self.ids, 'download_list'):
            download_list = self.ids.download_list
            download_list.clear_widgets()

            # Add items for each download
            if self.download_manager:
                for download_item in self.download_manager.downloads:
                    download_card = DownloadCard(download_item=download_item, download_screen=self)
                    download_list.add_widget(download_card)

    def update_history_list(self):
        """Update the history list UI"""
        if hasattr(self.ids, 'history_list'):
            history_list = self.ids.history_list
            history_list.clear_widgets()

            # Add items for each history entry
            if self.download_manager and hasattr(self.download_manager, 'download_history'):
                # Sort history by download date (newest first)
                sorted_history = sorted(
                    self.download_manager.download_history,
                    key=lambda x: x.get('download_date', ''),
                    reverse=True
                )

                for history_item in sorted_history:
                    history_card = HistoryCard(history_item=history_item, download_screen=self)
                    history_list.add_widget(history_card)

    def check_youtube_formats(self, dt):
        """Check if any YouTube downloads have formats available"""
        if not self.download_manager:
            return

        for download_item in self.download_manager.downloads:
            # If it's a YouTube download with formats available but no format selected
            if (download_item.is_youtube and download_item.available_formats and
                not download_item.selected_format and
                download_item.status == "pending" and
                not self.format_dialog):
                # Show format selection dialog
                self.show_format_selection_dialog(download_item)
                break

    def show_format_selection_dialog(self, download_item):
        """Show dialog to select download format"""
        if self.format_dialog:
            return

        self.current_youtube_item = download_item
        self.format_dialog = MDDialog(
            title="Select Format",
            type="custom",
            content_cls=FormatSelectionContent(
                download_item=download_item,
                download_screen=self
            ),
            size_hint=(0.9, None),
            height=500,
            buttons=[
                MDFlatButton(
                    text="CANCEL",
                    theme_text_color="Custom",
                    text_color=MDApp.get_running_app().root.get_primary_color(),
                    on_release=lambda x: self.dismiss_format_dialog()
                )
            ],
        )
        self.format_dialog.open()

    def dismiss_format_dialog(self):
        """Dismiss the format selection dialog"""
        if self.format_dialog:
            self.format_dialog.dismiss()
            self.format_dialog = None
            self.current_youtube_item = None

    def show_add_url_dialog(self):
        """Show dialog to add a new download URL"""
        if not self.url_dialog:
            self.url_dialog = MDDialog(
                title="Add Download URL",
                type="custom",
                content_cls=URLInputContent(),
                size_hint=(0.9, None),
                height=280,  # Increased height for better spacing
                buttons=[
                    MDFlatButton(
                        text="CANCEL",
                        theme_text_color="Custom",
                        text_color=MDApp.get_running_app().root.get_primary_color(),
                        font_name='NotoNaskhArabic-VariableFont_wght',
                        font_size=sp(14),
                        on_release=lambda x: self.url_dialog.dismiss()
                    ),
                    MDRaisedButton(
                        text="DOWNLOAD",
                        theme_text_color="Custom",
                        text_color=(1, 1, 1, 1),
                        font_name='NotoNaskhArabic-VariableFont_wght',
                        font_size=sp(14),
                        md_bg_color=MDApp.get_running_app().root.get_primary_color(),
                        elevation=2,
                        on_release=lambda x: self.add_download_from_dialog()
                    ),
                ],
            )
        self.url_dialog.open()

    def add_download_from_dialog(self):
        """Add a download from the URL dialog"""
        if self.url_dialog and self.url_dialog.content_cls:
            url = self.url_dialog.content_cls.ids.url_input.text.strip()
            if url:
                # Check if it's a YouTube URL but libraries are not available
                is_youtube_url = self._is_youtube_url(url)
                if is_youtube_url and not self._check_youtube_libraries():
                    # Show message about installing libraries
                    self.show_youtube_library_dialog()
                    self.url_dialog.dismiss()
                    return

                if self.download_manager:
                    success, download_item = self.download_manager.add_download(url)
                    if success:
                        # Update the list
                        self.update_download_list()
                        # Close the dialog
                        self.url_dialog.dismiss()

                        # If it's a YouTube URL, show format selection dialog
                        if download_item and download_item.is_youtube:
                            # Format dialog will be shown automatically by check_youtube_formats
                            pass
                    else:
                        # Show error
                        self.show_error_dialog("Invalid URL format")
                else:
                    self.show_error_dialog("Download manager not initialized")
            else:
                self.show_error_dialog("Please enter a URL")

    def _is_youtube_url(self, url):
        """Check if the URL is a YouTube URL"""
        import re
        from urllib.parse import urlparse

        # List of YouTube domains
        youtube_domains = [
            'youtube.com',
            'youtu.be',
            'www.youtube.com',
            'm.youtube.com',
            'youtube-nocookie.com',
            'www.youtube-nocookie.com'
        ]

        try:
            # Parse the URL
            parsed_url = urlparse(url)

            # Check if the domain is a YouTube domain
            is_youtube_domain = any(domain in parsed_url.netloc for domain in youtube_domains)

            # Additional check for YouTube video ID patterns
            if is_youtube_domain:
                return True

            # Check for YouTube video ID in the URL (for cases where domain check might fail)
            youtube_patterns = [
                r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',  # Standard YouTube video ID pattern
                r'^([0-9A-Za-z_-]{11})$',           # Just the video ID
                r'youtu\.be\/([0-9A-Za-z_-]{11})',  # Short URL format
                r'embed\/([0-9A-Za-z_-]{11})'       # Embed format
            ]

            for pattern in youtube_patterns:
                if re.search(pattern, url):
                    return True

            return False
        except Exception as e:
            logger.warning(f"Error checking if URL is YouTube: {e}")
            # Default to False if there's an error
            return False

    def _check_youtube_libraries(self):
        """Check if YouTube download libraries are available"""
        try:
            import yt_dlp
            return True
        except ImportError:
            try:
                from pytube import YouTube
                return True
            except ImportError:
                return False

    def show_youtube_library_dialog(self):
        """Show dialog about installing YouTube download libraries"""
        dialog = MDDialog(
            title="YouTube Download Libraries Required",
            text=(
                "To download videos from YouTube, you need to install the yt-dlp library.\n\n"
                "Please run the following command in your terminal or command prompt:\n\n"
                "pip install yt-dlp\n\n"
                "For MP3 conversion support, you also need to install FFmpeg:\n\n"
                "- Windows: Download from https://ffmpeg.org/download.html\n"
                "- macOS: brew install ffmpeg\n"
                "- Linux: sudo apt install ffmpeg\n\n"
                "After installation, restart the application."
            ),
            buttons=[
                MDFlatButton(
                    text="OK",
                    theme_text_color="Custom",
                    text_color=MDApp.get_running_app().root.get_primary_color(),
                    on_release=lambda x: dialog.dismiss()
                ),
            ],
        )
        dialog.open()

    def show_error_dialog(self, error_message):
        """Show an error dialog"""
        # Preparar textos con soporte para árabe
        title = "Error"

        # Verificar si los textos contienen caracteres árabes
        is_title_arabic = contains_arabic(title)
        is_error_arabic = contains_arabic(error_message)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_title = get_display_text(title) if is_title_arabic else title
        display_error = get_display_text(error_message) if is_error_arabic else error_message

        error_dialog = MDDialog(
            title=display_title,
            text=display_error,
            buttons=[
                MDFlatButton(
                    text="OK",
                    theme_text_color="Custom",
                    text_color=MDApp.get_running_app().root.get_primary_color(),
                    font_name='NotoNaskhArabic-VariableFont_wght',
                    on_release=lambda x: error_dialog.dismiss()
                ),
            ],
        )
        error_dialog.open()

    def clear_completed_downloads(self):
        """Clear all completed downloads"""
        if self.download_manager:
            self.download_manager.clear_completed()
            self.update_download_list()

    def clear_download_history(self):
        """Clear the entire download history"""
        if self.download_manager:
            # Preparar textos con soporte para árabe
            title = "Clear Download History"
            message = "Are you sure you want to clear the entire download history? This action cannot be undone."

            # Verificar si los textos contienen caracteres árabes
            is_title_arabic = contains_arabic(title)
            is_message_arabic = contains_arabic(message)

            # Aplicar el algoritmo bidireccional y el reformado de texto árabe
            display_title = get_display_text(title) if is_title_arabic else title
            display_message = get_display_text(message) if is_message_arabic else message

            # Show confirmation dialog
            confirm_dialog = MDDialog(
                title=display_title,
                text=display_message,
                buttons=[
                    MDFlatButton(
                        text="CANCEL",
                        theme_text_color="Custom",
                        text_color=MDApp.get_running_app().root.get_primary_color(),
                        font_name='NotoNaskhArabic-VariableFont_wght',
                        on_release=lambda x: confirm_dialog.dismiss()
                    ),
                    MDRaisedButton(
                        text="CLEAR",
                        theme_text_color="Custom",
                        text_color=(1, 1, 1, 1),
                        font_name='NotoNaskhArabic-VariableFont_wght',
                        on_release=lambda x: self._perform_clear_history(confirm_dialog)
                    ),
                ],
            )
            confirm_dialog.open()

    def _perform_clear_history(self, dialog):
        """Actually perform the history clearing after confirmation"""
        if self.download_manager:
            # Clear the history list
            self.download_manager.download_history = []

            # Save the empty history
            self.download_manager.save_download_history()

            # Update the UI
            self.update_history_list()

            # Preparar texto con soporte para árabe
            message = "Download history has been cleared"

            # Verificar si el texto contiene caracteres árabes
            is_message_arabic = contains_arabic(message)

            # Aplicar el algoritmo bidireccional y el reformado de texto árabe
            display_message = get_display_text(message) if is_message_arabic else message

            # Show confirmation
            Snackbar(
                text=display_message,
                duration=2,
                font_name='NotoNaskhArabic-VariableFont_wght'
            ).open()

            # Dismiss the dialog
            dialog.dismiss()

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class FormatListItem(OneLineListItem):
    """List item for video format selection"""
    format_obj = ObjectProperty(None)

    def __init__(self, format_obj, **kwargs):
        self.format_obj = format_obj

        # Obtener la descripción del formato
        description = format_obj.description

        # Verificar si el texto contiene caracteres árabes
        is_arabic = contains_arabic(description)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_description = get_display_text(description) if is_arabic else description

        # Establecer el texto con el formato adecuado
        if 'text' not in kwargs:
            kwargs['text'] = display_description

        # Inicializar la clase padre
        super(FormatListItem, self).__init__(**kwargs)

        # Configurar propiedades adicionales
        self.font_size = sp(14)  # Tamaño de fuente reducido
        self.font_name = 'NotoNaskhArabic-VariableFont_wght'

        # Configurar alineación según el idioma
        if is_arabic:
            self.halign = 'right'

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class FormatSelectionContent(BoxLayout):
    """Content for format selection dialog"""
    download_item = ObjectProperty(None)
    download_screen = ObjectProperty(None)

    def __init__(self, download_item, download_screen, **kwargs):
        super(FormatSelectionContent, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = [20, 20, 20, 20]
        self.spacing = 10
        self.size_hint_y = None
        self.height = 400
        self.download_item = download_item
        self.download_screen = download_screen

        # Title
        title_text = "Select Download Format"
        # Verificar si el texto contiene caracteres árabes
        is_arabic = contains_arabic(title_text)
        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_title_text = get_display_text(title_text) if is_arabic else title_text

        title = MDLabel(
            text=display_title_text,
            halign="center",
            theme_text_color="Primary",
            font_style="H6",
            font_name='NotoNaskhArabic-VariableFont_wght',
            size_hint_y=None,
            height=40
        )
        self.add_widget(title)

        # Loading spinner
        self.spinner = MDSpinner(
            size_hint=(None, None),
            size=(46, 46),
            pos_hint={'center_x': .5, 'center_y': .5},
            active=True if download_item.status == "fetching_formats" else False
        )

        # Create a container for the spinner
        spinner_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=60,
            padding=[0, 10, 0, 10]
        )
        spinner_container.add_widget(self.spinner)
        self.add_widget(spinner_container)

        # Format list
        self.format_list = MDList()

        # Scroll view for formats
        scroll = ScrollView(
            size_hint=(1, None),
            size=(400, 300)
        )
        scroll.add_widget(self.format_list)
        self.add_widget(scroll)

        # Populate the list if formats are available
        if download_item.available_formats:
            self.populate_formats()

    def populate_formats(self):
        """Populate the format list with available formats"""
        self.spinner.active = False
        self.format_list.clear_widgets()

        # Add audio-only formats first
        audio_formats = [f for f in self.download_item.available_formats if f.is_audio_only]
        if audio_formats:
            header_text = "Audio Only"
            # Verificar si el texto contiene caracteres árabes
            is_arabic = contains_arabic(header_text)
            # Aplicar el algoritmo bidireccional y el reformado de texto árabe
            display_header_text = get_display_text(header_text) if is_arabic else header_text

            audio_header = MDLabel(
                text=display_header_text,
                theme_text_color="Secondary",
                font_style="Body1",
                font_size=sp(14),  # Tamaño de fuente reducido
                font_name='NotoNaskhArabic-VariableFont_wght',
                halign='right' if is_arabic else 'left',
                size_hint_y=None,
                height=30,
                padding=[20, 0, 0, 0]
            )
            self.format_list.add_widget(audio_header)

            for format_obj in audio_formats:
                item = FormatListItem(format_obj=format_obj)
                item.bind(on_release=self.on_format_selected)
                self.format_list.add_widget(item)

        # Add video with audio formats
        video_formats = [f for f in self.download_item.available_formats
                         if not f.is_audio_only and not f.is_video_only]
        if video_formats:
            header_text = "Video with Audio"
            # Verificar si el texto contiene caracteres árabes
            is_arabic = contains_arabic(header_text)
            # Aplicar el algoritmo bidireccional y el reformado de texto árabe
            display_header_text = get_display_text(header_text) if is_arabic else header_text

            video_header = MDLabel(
                text=display_header_text,
                theme_text_color="Secondary",
                font_style="Body1",
                font_size=sp(14),  # Tamaño de fuente reducido
                font_name='NotoNaskhArabic-VariableFont_wght',
                halign='right' if is_arabic else 'left',
                size_hint_y=None,
                height=30,
                padding=[20, 0, 0, 0]
            )
            self.format_list.add_widget(video_header)

            for format_obj in video_formats:
                item = FormatListItem(format_obj=format_obj)
                item.bind(on_release=self.on_format_selected)
                self.format_list.add_widget(item)

        # Add video-only formats
        video_only_formats = [f for f in self.download_item.available_formats if f.is_video_only]
        if video_only_formats:
            header_text = "Video Only (No Audio)"
            # Verificar si el texto contiene caracteres árabes
            is_arabic = contains_arabic(header_text)
            # Aplicar el algoritmo bidireccional y el reformado de texto árabe
            display_header_text = get_display_text(header_text) if is_arabic else header_text

            video_only_header = MDLabel(
                text=display_header_text,
                theme_text_color="Secondary",
                font_style="Body1",
                font_size=sp(14),  # Tamaño de fuente reducido
                font_name='NotoNaskhArabic-VariableFont_wght',
                halign='right' if is_arabic else 'left',
                size_hint_y=None,
                height=30,
                padding=[20, 0, 0, 0]
            )
            self.format_list.add_widget(video_only_header)

            for format_obj in video_only_formats:
                item = FormatListItem(format_obj=format_obj)
                item.bind(on_release=self.on_format_selected)
                self.format_list.add_widget(item)

    def on_format_selected(self, item):
        """Handle format selection"""
        if self.download_screen and self.download_screen.format_dialog:
            # Set the selected format
            if self.download_screen.download_manager:
                self.download_screen.download_manager.set_download_format(
                    self.download_item, item.format_obj.itag
                )

            # Close the dialog
            self.download_screen.format_dialog.dismiss()

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class URLInputContent(BoxLayout):
    """Content for URL input dialog"""
    def __init__(self, **kwargs):
        super(URLInputContent, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = [20, 20, 20, 20]
        self.spacing = 15
        self.size_hint_y = None
        self.height = 170  # Increased height for better spacing

        # URL input field with paste button
        self.ids = {}  # Create an ids dictionary

        # Add an icon at the top using a label with emoji instead of MDIcon
        icon_box = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=50,
            padding=[0, 0, 0, 10]
        )

        # Use a label with emoji instead of MDIcon
        icon_label = MDLabel(
            text="🔗",  # Link emoji
            halign='center',
            theme_text_color="Custom",
            text_color=MDApp.get_running_app().root.get_primary_color(),
            font_size=sp(36),
            size_hint=(1, 1)
        )
        icon_box.add_widget(icon_label)
        self.add_widget(icon_box)

        # Create a horizontal layout for the input field and paste button
        input_layout = BoxLayout(
            orientation='horizontal',
            spacing=10,
            size_hint_y=None,
            height=60
        )

        # Preparar textos con soporte para árabe
        hint_text = "Enter URL"
        helper_text = "Enter a YouTube URL or direct link to an audio file"

        # Verificar si los textos contienen caracteres árabes
        is_hint_arabic = contains_arabic(hint_text)
        is_helper_arabic = contains_arabic(helper_text)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_hint_text = get_display_text(hint_text) if is_hint_arabic else hint_text
        display_helper_text = get_display_text(helper_text) if is_helper_arabic else helper_text

        # URL input field
        url_input = MDTextField(
            hint_text=display_hint_text,
            helper_text=display_helper_text,
            helper_text_mode="on_focus",
            multiline=False,
            font_name='NotoNaskhArabic-VariableFont_wght',
            size_hint_x=0.8,
            halign="auto",  # Auto-detect text direction
            font_size=sp(16)
        )
        self.ids['url_input'] = url_input  # Add to ids dictionary
        input_layout.add_widget(url_input)

        # Paste button
        paste_button = MDRaisedButton(
            text="PASTE",
            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(13),
            on_release=self.paste_from_clipboard,
            size_hint_x=0.2,
            pos_hint={'center_y': 0.5},
            md_bg_color=MDApp.get_running_app().root.get_primary_color(),
            elevation=2
        )
        input_layout.add_widget(paste_button)

        # Add the input layout to the main layout
        self.add_widget(input_layout)

    def paste_from_clipboard(self, instance):
        """Paste text from clipboard into the URL input field"""
        try:
            from kivy.core.clipboard import Clipboard
            clipboard_text = Clipboard.paste()

            if clipboard_text:
                # Update the URL input field with the clipboard content
                self.ids.url_input.text = clipboard_text
            else:
                # Show a message if clipboard is empty
                from kivymd.uix.snackbar import Snackbar
                Snackbar(text="Clipboard is empty").open()
        except Exception as e:
            logger.error(f"Error pasting from clipboard: {e}")
            from kivymd.uix.snackbar import Snackbar
            Snackbar(text="Failed to paste from clipboard").open()

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class HistoryCard(MDCard):
    """Card widget for displaying a history item"""
    history_item = ObjectProperty(None)
    download_screen = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(HistoryCard, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = [20, 15, 20, 15]
        self.spacing = 12
        self.size_hint_y = None
        self.height = 130
        self.elevation = 2
        self.radius = [15, 15, 15, 15]
        self.ripple_behavior = True
        self.md_bg_color = [0.98, 0.98, 0.98, 1] if self.theme_cls.theme_style == "Light" else [0.15, 0.15, 0.15, 1]

        # Update the UI
        self.update_ui()

    def update_ui(self, *args):
        """Update the UI based on history item"""
        if not self.history_item:
            return

        # Clear existing widgets
        self.clear_widgets()

        # Top row with filename and date
        top_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=30)

        # Filename label
        filename = self.history_item.get('filename', 'Unknown file')

        # Verificar si el texto contiene caracteres árabes y aplicar el formato adecuado
        is_arabic = contains_arabic(filename)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_filename = get_display_text(filename) if is_arabic else filename

        filename_label = MDLabel(
            text=display_filename,
            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(14),  # Tamaño de fuente reducido
            halign='right' if is_arabic else 'left',
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        top_row.add_widget(filename_label)

        # Date label
        date_str = self.history_item.get('download_date', '')
        try:
            # Try to parse and format the date nicely
            date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            formatted_date = date_obj.strftime('%d %b %Y')
        except:
            formatted_date = date_str

        date_label = MDLabel(
            text=formatted_date,
            halign='right',
            theme_text_color="Secondary",
            size_hint_x=0.3
        )
        top_row.add_widget(date_label)
        self.add_widget(top_row)

        # Info row
        info_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=30)

        # File type and size info
        file_type = self.history_item.get('file_type', '').upper()
        file_size = self.history_item.get('file_size', 0)

        # Format file size
        if file_size < 1024:
            size_text = f"{file_size} B"
        elif file_size < 1024 * 1024:
            size_text = f"{file_size / 1024:.1f} KB"
        else:
            size_text = f"{file_size / (1024 * 1024):.1f} MB"

        info_text = f"{file_type} • {size_text}"
        if self.history_item.get('is_youtube', False):
            info_text += " • YouTube"

        # Verificar si el texto contiene caracteres árabes
        is_arabic = contains_arabic(info_text)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_info_text = get_display_text(info_text) if is_arabic else info_text

        info_label = MDLabel(
            text=display_info_text,
            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(12),  # Tamaño de fuente reducido
            halign='right' if is_arabic else 'left',
            theme_text_color="Secondary",
            size_hint_x=1
        )
        info_row.add_widget(info_label)
        self.add_widget(info_row)

        # Action buttons
        action_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=45, spacing=10)

        # Play button
        play_button = MDRaisedButton(
            text="PLAY",

            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(13),
            on_release=lambda x: self.play_download(),
            size_hint_x=0.5,
            md_bg_color=[0.2, 0.7, 0.2, 1],
            elevation=2
        )
        action_row.add_widget(play_button)

        # Delete from history button
        delete_button = MDRaisedButton(
            text="REMOVE",

            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(13),
            on_release=lambda x: self.remove_from_history(),
            size_hint_x=0.5,
            md_bg_color=[0.7, 0.3, 0.3, 1],
            elevation=2
        )
        action_row.add_widget(delete_button)
        self.add_widget(action_row)

    def play_download(self):
        """Play the downloaded file"""
        if self.history_item and 'full_path' in self.history_item:
            file_path = self.history_item['full_path']
            if os.path.exists(file_path):
                # Get the parent app
                app = MDApp.get_running_app()
                if hasattr(app, 'root') and hasattr(app.root, 'play_track'):
                    app.root.play_track(file_path)
                    # Go back to main screen
                    if hasattr(app.root.ids, 'screen_manager'):
                        app.root.ids.screen_manager.current = 'main'
            else:
                # Show error that file doesn't exist
                Snackbar(
                    text="File not found. It may have been moved or deleted.",
                    duration=3
                ).open()

    def remove_from_history(self):
        """Remove the item from download history"""
        if self.download_screen and self.download_screen.download_manager and self.history_item:
            # Find and remove the item from history
            for i, item in enumerate(self.download_screen.download_manager.download_history):
                if (item.get('url') == self.history_item.get('url') and
                    item.get('filename') == self.history_item.get('filename')):
                    self.download_screen.download_manager.download_history.pop(i)
                    break

            # Save the updated history
            self.download_screen.download_manager.save_download_history()

            # Update the history list
            self.download_screen.update_history_list()

            # Show confirmation
            Snackbar(
                text="Item removed from download history",
                duration=2
            ).open()

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class DownloadCard(MDCard):
    """Card widget for displaying a download item"""
    download_item = ObjectProperty(None)
    download_screen = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(DownloadCard, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = [20, 15, 20, 15]
        self.spacing = 12
        self.size_hint_y = None
        self.height = 160
        self.elevation = 2
        self.radius = [15, 15, 15, 15]
        self.ripple_behavior = True
        self.md_bg_color = [0.98, 0.98, 0.98, 1] if self.theme_cls.theme_style == "Light" else [0.15, 0.15, 0.15, 1]

        # Update the UI
        self.update_ui()

        # Schedule updates
        from kivy.clock import Clock
        self.update_event = Clock.schedule_interval(self.update_ui, 0.5)

    def update_ui(self, *args):
        """Update the UI based on download status"""
        if not self.download_item:
            return

        # Clear existing widgets
        self.clear_widgets()

        # Top row with filename and status
        top_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=30)

        # Filename label
        filename = self.download_item.filename

        # Verificar si el texto contiene caracteres árabes
        is_arabic = contains_arabic(filename)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_filename = get_display_text(filename) if is_arabic else filename

        filename_label = MDLabel(
            text=display_filename,
            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(14),  # Tamaño de fuente reducido
            halign='right' if is_arabic else 'left',
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        top_row.add_widget(filename_label)

        # Status label with appropriate color
        # Set status color based on status
        status_color = {
            "pending": [0.2, 0.6, 1, 1],  # Blue
            "downloading": [0, 0.7, 0.7, 1],  # Teal
            "completed": [0, 0.8, 0.2, 1],  # Green
            "error": [1, 0.2, 0.2, 1],  # Red
            "paused": [1, 0.6, 0, 1],  # Orange
            "canceled": [0.5, 0.5, 0.5, 1]  # Gray
        }.get(self.download_item.status, [0.5, 0.5, 0.5, 1])

        status_label = MDLabel(
            text=self.download_item.status.upper(),
            halign='right',
            theme_text_color="Custom",
            text_color=status_color,
            bold=True,
            size_hint_x=0.3
        )
        top_row.add_widget(status_label)
        self.add_widget(top_row)

        # Progress bar container
        progress_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=15,
            padding=[0, 2, 0, 2]
        )

        # Progress bar
        progress_bar = MDProgressBar(
            value=self.download_item.progress,
            color=MDApp.get_running_app().root.get_primary_color(),
            size_hint_y=None,
            height=10,
            radius=[5, 5, 5, 5]
        )
        progress_container.add_widget(progress_bar)
        self.add_widget(progress_container)

        # Info row
        info_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=30)

        # Size info
        size_text = f"{self.download_item.get_formatted_size()}"
        if self.download_item.status == "downloading":
            size_text = f"{self.download_item.downloaded_size / 1024 / 1024:.1f} MB / {size_text}"

        # Verificar si el texto contiene caracteres árabes
        is_arabic = contains_arabic(size_text)

        # Aplicar el algoritmo bidireccional y el reformado de texto árabe
        display_size_text = get_display_text(size_text) if is_arabic else size_text

        size_label = MDLabel(
            text=display_size_text,
            font_name='NotoNaskhArabic-VariableFont_wght',
            font_size=sp(12),  # Tamaño de fuente reducido
            halign='right' if is_arabic else 'left',
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        info_row.add_widget(size_label)

        # Speed and ETA (only for downloading)
        if self.download_item.status == "downloading":
            speed_text = f"{self.download_item.get_formatted_speed()} • {self.download_item.get_estimated_time()} left"
            # Verificar si el texto contiene caracteres árabes
            is_arabic = contains_arabic(speed_text)

            # Aplicar el algoritmo bidireccional y el reformado de texto árabe
            display_speed_text = get_display_text(speed_text) if is_arabic else speed_text

            speed_label = MDLabel(
                text=display_speed_text,
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(12),  # Tamaño de fuente reducido
                halign='right',  # Siempre alineado a la derecha para números
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            info_row.add_widget(speed_label)
        else:
            # Empty widget for spacing
            info_row.add_widget(BoxLayout(size_hint_x=0.5))

        self.add_widget(info_row)

        # Action buttons
        action_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=40)

        # Different buttons based on status
        if self.download_item.status == "fetching_formats":
            # Show loading message for YouTube format fetching
            loading_text = "Fetching available formats..."
            loading_label = MDLabel(
                text=loading_text,
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(12),  # Tamaño de fuente reducido
                halign="center",
                theme_text_color="Secondary",
                size_hint_x=1
            )
            action_row.add_widget(loading_label)

        elif self.download_item.status == "downloading":
            # Pause button
            pause_button = MDRaisedButton(
                text="PAUSE",
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.pause_download(),
                size_hint_x=0.5,
                md_bg_color=MDApp.get_running_app().root.get_primary_color(),
                elevation=2
            )
            action_row.add_widget(pause_button)

            # Cancel button
            cancel_button = MDRaisedButton(
                text="CANCEL",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.cancel_download(),
                size_hint_x=0.5,
                md_bg_color=[0.9, 0.3, 0.3, 1],
                elevation=2
            )
            action_row.add_widget(cancel_button)

        elif self.download_item.status == "paused":
            # Resume button
            resume_button = MDRaisedButton(
                text="RESUME",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.resume_download(),
                size_hint_x=0.5,
                md_bg_color=[0.2, 0.7, 0.2, 1],
                elevation=2
            )
            action_row.add_widget(resume_button)

            # Cancel button
            cancel_button = MDRaisedButton(
                text="CANCEL",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.cancel_download(),
                size_hint_x=0.5,
                md_bg_color=[0.9, 0.3, 0.3, 1],
                elevation=2
            )
            action_row.add_widget(cancel_button)

        elif self.download_item.status == "completed":
            # Play button
            play_button = MDRaisedButton(
                text="PLAY",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.play_download(),
                size_hint_x=0.5,
                md_bg_color=[0.2, 0.7, 0.2, 1],
                elevation=2
            )
            action_row.add_widget(play_button)

            # Remove button
            remove_button = MDRaisedButton(
                text="REMOVE",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.remove_download(),
                size_hint_x=0.5,
                md_bg_color=[0.7, 0.3, 0.3, 1],
                elevation=2
            )
            action_row.add_widget(remove_button)

        elif self.download_item.status == "error":
            # Retry button
            retry_button = MDRaisedButton(
                text="RETRY",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.retry_download(),
                size_hint_x=0.5,
                md_bg_color=MDApp.get_running_app().root.get_primary_color(),
                elevation=2
            )
            action_row.add_widget(retry_button)

            # Remove button
            remove_button = MDRaisedButton(
                text="REMOVE",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.remove_download(),
                size_hint_x=0.5,
                md_bg_color=[0.7, 0.3, 0.3, 1],
                elevation=2
            )
            action_row.add_widget(remove_button)

        elif self.download_item.status == "canceled":
            # Remove button
            remove_button = MDRaisedButton(
                text="REMOVE",

                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(13),
                on_release=lambda x: self.remove_download(),
                size_hint_x=1,
                md_bg_color=[0.7, 0.3, 0.3, 1],
                elevation=2
            )
            action_row.add_widget(remove_button)

        elif self.download_item.status == "pending":
            # For YouTube downloads with available formats but no format selected
            if (self.download_item.is_youtube and self.download_item.available_formats and
                not self.download_item.selected_format):
                # Select format button
                select_format_button = MDRaisedButton(
                    text="SELECT FORMAT",

                    font_name='NotoNaskhArabic-VariableFont_wght',
                    font_size=sp(13),
                    on_release=lambda x: self.show_format_selection(),
                    size_hint_x=0.7,
                    md_bg_color=MDApp.get_running_app().root.get_primary_color(),
                    elevation=2
                )
                action_row.add_widget(select_format_button)

                # Cancel button
                cancel_button = MDRaisedButton(
                    text="CANCEL",

                    font_name='NotoNaskhArabic-VariableFont_wght',
                    font_size=sp(13),
                    on_release=lambda x: self.cancel_download(),
                    size_hint_x=0.3,
                    md_bg_color=[0.9, 0.3, 0.3, 1],
                    elevation=2
                )
                action_row.add_widget(cancel_button)
            else:
                # Regular pending download
                cancel_button = MDRaisedButton(
                    text="CANCEL",

                    font_name='NotoNaskhArabic-VariableFont_wght',
                    font_size=sp(13),
                    on_release=lambda x: self.cancel_download(),
                    size_hint_x=1,
                    md_bg_color=[0.9, 0.3, 0.3, 1],
                    elevation=2
                )
                action_row.add_widget(cancel_button)

        self.add_widget(action_row)

    def pause_download(self):
        """Pause the download"""
        if self.download_screen and self.download_screen.download_manager:
            self.download_screen.download_manager.pause_download(self.download_item)

    def resume_download(self):
        """Resume the download"""
        if self.download_screen and self.download_screen.download_manager:
            self.download_screen.download_manager.resume_download(self.download_item)

    def cancel_download(self):
        """Cancel the download"""
        if self.download_screen and self.download_screen.download_manager:
            self.download_screen.download_manager.cancel_download(self.download_item)

    def retry_download(self):
        """Retry the download"""
        if self.download_screen and self.download_screen.download_manager:
            self.download_screen.download_manager.retry_download(self.download_item)

    def remove_download(self):
        """Remove the download"""
        if self.download_screen and self.download_screen.download_manager:
            self.download_screen.download_manager.remove_download(self.download_item)
            # Update the list
            self.download_screen.update_download_list()

    def show_format_selection(self):
        """Show format selection dialog"""
        if self.download_screen and self.download_item.is_youtube:
            self.download_screen.show_format_selection_dialog(self.download_item)

    def play_download(self):
        """Play the downloaded file"""
        if self.download_item and self.download_item.status == "completed":
            # Get the parent app
            app = MDApp.get_running_app()
            if hasattr(app, 'root') and hasattr(app.root, 'play_track'):
                app.root.play_track(self.download_item.full_path)
                # Go back to main screen
                if hasattr(app.root.ids, 'screen_manager'):
                    app.root.ids.screen_manager.current = 'main'

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
