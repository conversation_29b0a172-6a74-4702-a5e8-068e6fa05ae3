import os
from PIL import Image, ImageDraw, ImageFont
import io

def create_default_cover():
    """Create a default album cover image and save it to the images directory"""
    # Create a new image with a dark background
    img_size = 500
    img = Image.new('RGB', (img_size, img_size), color=(40, 40, 40))
    draw = ImageDraw.Draw(img)
    
    # Draw a music note icon in the center
    icon_size = img_size // 2
    icon_pos = (img_size // 2 - icon_size // 2, img_size // 2 - icon_size // 2)
    
    # Draw a circle for the note head
    circle_radius = icon_size // 4
    circle_pos = (icon_pos[0] + icon_size // 2, icon_pos[1] + icon_size // 2)
    draw.ellipse((circle_pos[0] - circle_radius, 
                  circle_pos[1] - circle_radius,
                  circle_pos[0] + circle_radius, 
                  circle_pos[1] + circle_radius), 
                 fill=(200, 200, 200))
    
    # Draw the note stem
    stem_width = circle_radius // 3
    stem_height = icon_size // 2
    draw.rectangle((circle_pos[0] + circle_radius - stem_width,
                   circle_pos[1] - stem_height,
                   circle_pos[0] + circle_radius,
                   circle_pos[1]),
                  fill=(200, 200, 200))
    
    # Add text
    try:
        # Try to use a system font
        font = ImageFont.truetype("arial.ttf", size=img_size // 10)
    except:
        # Fall back to default font
        font = ImageFont.load_default()
    
    text = "No Cover"
    text_width, text_height = draw.textsize(text, font=font) if hasattr(draw, 'textsize') else font.getsize(text)
    text_position = ((img_size - text_width) // 2, img_size - text_height - 50)
    draw.text(text_position, text, fill=(200, 200, 200), font=font)
    
    # Save the image
    images_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(images_dir, "default_album_cover.png")
    img.save(output_path)
    print(f"Default album cover created at: {output_path}")
    return output_path

if __name__ == "__main__":
    create_default_cover()
