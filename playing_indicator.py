from kivy.uix.widget import Widget
from kivy.graphics import Color, Ellipse
from kivy.properties import NumericProperty
from kivy.animation import Animation
from kivy.clock import Clock
from kivy.metrics import dp

class PlayingIndicator(Widget):
    """
    A visual indicator for the currently playing song.
    Shows animated pulsating circles to indicate playback.
    """
    pulse_size = NumericProperty(dp(20))
    
    def __init__(self, **kwargs):
        super(PlayingIndicator, self).__init__(**kwargs)
        self._animations = []
        self._circles = []
        self._colors = []
        
        # Schedule setup after widget is fully initialized
        Clock.schedule_once(self._setup, 0)
        
    def _setup(self, dt):
        """Set up the visual effect with multiple circles"""
        # Clear canvas
        self.canvas.clear()
        
        # Create three pulsating circles with different phases
        num_circles = 3
        base_color = [0.2, 0.7, 1, 1]  # Light blue
        
        with self.canvas:
            for i in range(num_circles):
                # Create color instruction with decreasing opacity
                alpha = 0.7 - (i * 0.2)
                color = Color(base_color[0], base_color[1], base_color[2], alpha)
                self._colors.append(color)
                
                # Create circle with initial size
                initial_size = dp(5 + i * 5)
                circle = Ellipse(pos=self.center, size=(initial_size, initial_size))
                self._circles.append(circle)
                
                # Create and start animation with delay based on index
                self._start_animation(i, delay=i * 0.2)
        
        # Bind position and size changes
        self.bind(pos=self._update_circles, size=self._update_circles)
        
    def _start_animation(self, index, delay=0):
        """Start pulsating animation for a circle with optional delay"""
        # Cancel existing animation if any
        if index < len(self._animations) and self._animations[index]:
            self._animations[index].cancel(self)
            
        # Create animation sequence
        grow = Animation(pulse_size=dp(30), duration=0.8)
        shrink = Animation(pulse_size=dp(15), duration=0.8)
        sequence = grow + shrink
        sequence.repeat = True
        
        # Store animation reference
        if index >= len(self._animations):
            self._animations.append(sequence)
        else:
            self._animations[index] = sequence
            
        # Start animation with delay
        if delay > 0:
            Clock.schedule_once(lambda dt: sequence.start(self), delay)
        else:
            sequence.start(self)
            
        # Bind animation progress to update function
        sequence.bind(on_progress=lambda *args: self._update_circle(index))
        
    def _update_circles(self, *args):
        """Update all circles when widget position or size changes"""
        for i in range(len(self._circles)):
            self._update_circle(i)
            
    def _update_circle(self, index):
        """Update a specific circle's position and size"""
        if index < len(self._circles):
            circle = self._circles[index]
            
            # Calculate size based on index and pulse size
            size_factor = 1.0 - (index * 0.2)
            size = self.pulse_size * size_factor
            
            # Update circle position and size
            circle.size = (size, size)
            circle.pos = (
                self.center_x - size / 2,
                self.center_y - size / 2
            )
            
    def on_pulse_size(self, instance, value):
        """Handle changes to pulse_size property"""
        self._update_circles()
        
    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
