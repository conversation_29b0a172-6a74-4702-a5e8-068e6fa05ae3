#:import dp kivy.metrics.dp
#:import sp kivy.metrics.sp

<URLInputContent>:
    orientation: 'vertical'
    padding: dp(20)
    spacing: dp(15)
    size_hint_y: None
    height: dp(150)

<DownloadScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: 0
        spacing: 0

        # Top app bar
        MDTopAppBar:
            title: "Downloads"
            left_action_items: [["arrow-left", lambda x: app.root.back_to_main()]]
            right_action_items: [["plus", lambda x: root.show_add_url_dialog()]]
            elevation: dp(4)
            md_bg_color: app.root.get_primary_color()
            specific_text_color: app.root.get_text_color()

        # Tabs
        MDTabs:
            id: tabs
            on_tab_switch: root.on_tabs_changed(*args)
            background_color: app.root.get_bg_color()
            indicator_color: app.root.get_primary_color()
            tab_hint_x: True
            elevation: 2

            # Active Downloads Tab
            ActiveDownloadsTab:
                title: "Active Downloads"
                download_screen: root

                MDBoxLayout:
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(15)
                    md_bg_color: app.root.get_bg_color()

                    # Action buttons
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(50)
                        spacing: dp(15)
                        padding: [dp(10), dp(5), dp(10), dp(5)]
                        md_bg_color: app.root.get_bg_color()

                        MDRaisedButton:
                            text: "ADD URL"
                            icon: "plus"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            font_size: sp(14)
                            on_release: root.show_add_url_dialog()
                            size_hint_x: 0.5
                            md_bg_color: app.root.get_primary_color()
                            elevation: 2

                        MDRaisedButton:
                            text: "CLEAR COMPLETED"
                            icon: "delete-sweep"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            font_size: sp(14)
                            on_release: root.clear_completed_downloads()
                            size_hint_x: 0.5
                            md_bg_color: app.root.get_primary_color()
                            elevation: 2

                    # Download list
                    ScrollView:
                        do_scroll_x: False

                        MDGridLayout:
                            id: download_list
                            cols: 1
                            spacing: dp(10)
                            padding: dp(10)
                            size_hint_y: None
                            height: self.minimum_height

                            # Empty state message
                            MDBoxLayout:
                                id: empty_state
                                orientation: 'vertical'
                                size_hint_y: None
                                height: dp(250)
                                padding: dp(20)
                                spacing: dp(15)
                                opacity: 1 if not root.download_manager or not root.download_manager.downloads else 0

                                MDBoxLayout:
                                    orientation: 'vertical'
                                    size_hint_y: None
                                    height: dp(100)
                                    padding: dp(10)

                                    MDIcon:
                                        icon: "download-off"
                                        theme_text_color: "Custom"
                                        text_color: app.root.get_primary_color()
                                        font_size: sp(64)
                                        halign: 'center'
                                        valign: 'middle'
                                        size_hint: (1, 1)

                                MDLabel:
                                    text: "No active downloads"
                                    halign: 'center'
                                    theme_text_color: "Primary"
                                    font_style: 'H5'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    font_size: sp(22)
                                    size_hint_y: None
                                    height: self.texture_size[1]

                                MDLabel:
                                    text: "Click the ADD URL button to add a download URL"
                                    halign: 'center'
                                    theme_text_color: "Secondary"
                                    font_style: 'Body1'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    font_size: sp(14)
                                    size_hint_y: None
                                    height: self.texture_size[1]

                                MDRaisedButton:
                                    text: "ADD URL"
                                    icon: "plus"
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    font_size: sp(14)
                                    on_release: root.show_add_url_dialog()
                                    size_hint_x: None
                                    width: dp(150)
                                    pos_hint: {'center_x': 0.5}
                                    md_bg_color: app.root.get_primary_color()
                                    elevation: 2

            # History Tab
            HistoryTab:
                title: "History"
                download_screen: root

                MDBoxLayout:
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(15)
                    md_bg_color: app.root.get_bg_color()

                    # Action buttons
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(50)
                        spacing: dp(15)
                        padding: [dp(10), dp(5), dp(10), dp(5)]
                        md_bg_color: app.root.get_bg_color()

                        MDRaisedButton:
                            text: "CLEAR HISTORY"
                            icon: "delete-sweep"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            font_size: sp(14)
                            on_release: root.clear_download_history()
                            size_hint_x: 1.0
                            md_bg_color: app.root.get_primary_color()
                            elevation: 2

                    # History list
                    ScrollView:
                        do_scroll_x: False

                        MDGridLayout:
                            id: history_list
                            cols: 1
                            spacing: dp(10)
                            padding: dp(10)
                            size_hint_y: None
                            height: self.minimum_height

                            # Empty state message
                            MDBoxLayout:
                                id: history_empty_state
                                orientation: 'vertical'
                                size_hint_y: None
                                height: dp(250)
                                padding: dp(20)
                                spacing: dp(15)
                                opacity: 1 if not root.download_manager or not root.download_manager.download_history else 0

                                MDBoxLayout:
                                    orientation: 'vertical'
                                    size_hint_y: None
                                    height: dp(100)
                                    padding: dp(10)

                                    MDIcon:
                                        icon: "history-off"
                                        theme_text_color: "Custom"
                                        text_color: app.root.get_primary_color()
                                        font_size: sp(64)
                                        halign: 'center'
                                        valign: 'middle'
                                        size_hint: (1, 1)

                                MDLabel:
                                    text: "No download history"
                                    halign: 'center'
                                    theme_text_color: "Primary"
                                    font_style: 'H5'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    font_size: sp(22)
                                    size_hint_y: None
                                    height: self.texture_size[1]

                                MDLabel:
                                    text: "Your completed downloads will appear here"
                                    halign: 'center'
                                    theme_text_color: "Secondary"
                                    font_style: 'Body1'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    font_size: sp(14)
                                    size_hint_y: None
                                    height: self.texture_size[1]
