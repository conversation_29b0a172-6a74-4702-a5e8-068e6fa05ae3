from kivy.event import EventDispatcher
from kivy.properties import StringProperty
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import OneLineAvatarIconListItem
from kivymd.uix.button import MDFlatButton
from kivy.clock import Clock
from kivy.metrics import dp
import json
import os

# Import the ColorCircle class from our new module
from color_circle import ColorCircle

class ThemeItem(OneLineAvatarIconListItem):
    divider = None

    def __init__(self, theme_data, **kwargs):
        super().__init__(**kwargs)
        self.theme_data = theme_data
        self.text = theme_data["display"]

        # Use the imported ColorCircle class
        color_circle = ColorCircle(color=theme_data["color"])
        self.add_widget(color_circle)

class ThemeManager(EventDispatcher):
    theme_name = StringProperty("Blue")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.load_theme()
        self._theme_dialog = None

    def get_available_themes(self):
        return [
            {"name": "DeepPurple", "display": "Deep Purple", "color": [0.4, 0.2, 0.6, 1]},
            {"name": "Teal", "display": "Teal", "color": [0, 0.6, 0.6, 1]},
            {"name": "Indigo", "display": "Indigo", "color": [0.25, 0.31, 0.71, 1]},
            {"name": "BlueGray", "display": "Blue Gray", "color": [0.38, 0.49, 0.55, 1]},
            {"name": "Orange", "display": "Orange", "color": [1, 0.6, 0, 1]},
            {"name": "Red", "display": "Red", "color": [0.9, 0.2, 0.2, 1]},
            {"name": "Pink", "display": "Pink", "color": [0.91, 0.12, 0.39, 1]},
            {"name": "Purple", "display": "Purple", "color": [0.55, 0.14, 0.59, 1]},
            {"name": "Blue", "display": "Blue", "color": [0.13, 0.59, 0.95, 1]},
            {"name": "LightBlue", "display": "Light Blue", "color": [0.01, 0.66, 0.96, 1]},
            {"name": "Cyan", "display": "Cyan", "color": [0, 0.74, 0.83, 1]},
            {"name": "Green", "display": "Green", "color": [0.3, 0.69, 0.31, 1]},
            {"name": "LightGreen", "display": "Light Green", "color": [0.55, 0.76, 0.29, 1]},
            {"name": "Lime", "display": "Lime", "color": [0.8, 0.86, 0.22, 1]},
            {"name": "Yellow", "display": "Yellow", "color": [1, 0.92, 0.23, 1]},
            {"name": "Amber", "display": "Amber", "color": [1, 0.76, 0.03, 1]},
            {"name": "Brown", "display": "Brown", "color": [0.47, 0.33, 0.28, 1]},
            {"name": "Gray", "display": "Gray", "color": [0.62, 0.62, 0.62, 1]}
        ]

    def show_theme_selection_dialog(self):
        themes = self.get_available_themes()
        theme_items = []

        for theme in themes:
            def make_select_callback(theme_name):
                return lambda x: self.apply_theme(theme_name)

            item = ThemeItem(
                theme_data=theme,
                on_release=make_select_callback(theme["name"])
            )
            theme_items.append(item)

        dialog = MDDialog(
            title="Select Theme",
            type="simple",
            items=theme_items,
            buttons=[
                MDFlatButton(
                    text="CANCEL",
                    theme_text_color="Custom",
                    text_color=self.get_primary_color(),
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )

        self._theme_dialog = dialog
        dialog.open()

    def save_theme(self):
        try:
            with open("theme.json", "w", encoding="utf-8") as file:
                json.dump({"theme_name": self.theme_name}, file)
        except Exception as e:
            print(f"Error saving theme: {e}")

    def load_theme(self):
        if os.path.exists("theme.json"):
            try:
                with open("theme.json", "r", encoding="utf-8") as file:
                    data = json.load(file)
                self.theme_name = data.get("theme_name", "Blue")
            except Exception as e:
                print(f"Error loading theme: {e}")

    def apply_theme(self, theme_name):
        self.theme_name = theme_name
        self.save_theme()

        if hasattr(self, '_theme_dialog') and self._theme_dialog:
            self._theme_dialog.dismiss()
            self._theme_dialog = None

    def get_primary_color(self):
        from kivymd.app import MDApp
        app = MDApp.get_running_app()
        return app.theme_cls.primary_color