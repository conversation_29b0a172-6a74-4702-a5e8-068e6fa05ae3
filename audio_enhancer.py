import os
import subprocess
import logging
import tempfile
import numpy as np
from kivy.utils import platform
from threading import Thread
from queue import Queue

# Configurar el logger
logger = logging.getLogger(__name__)

class AudioEnhancer:
    """
    Clase para mejorar la calidad del audio en la aplicación de reproductor de música.
    Proporciona funcionalidades como ecualización, normalización de volumen y mejora de audio.
    """
    
    def __init__(self, ffmpeg_path=None):
        """
        Inicializa el mejorador de audio.
        
        Args:
            ffmpeg_path: Ruta al ejecutable de FFmpeg (opcional)
        """
        self.ffmpeg_path = ffmpeg_path
        self.eq_settings = {
            'bass': 0,      # -10 a +10 (dB)
            'mid': 0,       # -10 a +10 (dB)
            'treble': 0,    # -10 a +10 (dB)
            'gain': 0       # -10 a +10 (dB)
        }
        self.volume_normalization = False
        self.audio_buffer_size = 4096  # Tamaño del buffer en bytes (valor predeterminado)
        self.processing_queue = Queue()
        self.processing_thread = None
        self.is_processing = False
        
        # Iniciar el hilo de procesamiento
        self._start_processing_thread()
    
    def _start_processing_thread(self):
        """Inicia el hilo de procesamiento de audio en segundo plano"""
        self.is_processing = True
        self.processing_thread = Thread(target=self._process_queue, daemon=True)
        self.processing_thread.start()
    
    def _process_queue(self):
        """Procesa la cola de tareas de procesamiento de audio"""
        while self.is_processing:
            try:
                task = self.processing_queue.get(timeout=1)
                if task:
                    func, args, kwargs, callback = task
                    try:
                        result = func(*args, **kwargs)
                        if callback:
                            callback(result)
                    except Exception as e:
                        logger.error(f"Error en el procesamiento de audio: {e}")
                self.processing_queue.task_done()
            except:
                # Tiempo de espera agotado, continuar
                pass
    
    def stop(self):
        """Detiene el hilo de procesamiento"""
        self.is_processing = False
        if self.processing_thread:
            self.processing_thread.join(timeout=2)
    
    def set_equalizer(self, bass=None, mid=None, treble=None, gain=None):
        """
        Configura los ajustes del ecualizador.
        
        Args:
            bass: Nivel de graves (-10 a +10 dB)
            mid: Nivel de medios (-10 a +10 dB)
            treble: Nivel de agudos (-10 a +10 dB)
            gain: Ganancia general (-10 a +10 dB)
        """
        if bass is not None:
            self.eq_settings['bass'] = max(-10, min(10, bass))
        if mid is not None:
            self.eq_settings['mid'] = max(-10, min(10, mid))
        if treble is not None:
            self.eq_settings['treble'] = max(-10, min(10, treble))
        if gain is not None:
            self.eq_settings['gain'] = max(-10, min(10, gain))
        
        logger.info(f"Ecualizador configurado: {self.eq_settings}")
        return self.eq_settings
    
    def set_volume_normalization(self, enabled):
        """
        Activa o desactiva la normalización de volumen.
        
        Args:
            enabled: True para activar, False para desactivar
        """
        self.volume_normalization = enabled
        logger.info(f"Normalización de volumen: {'activada' if enabled else 'desactivada'}")
        return self.volume_normalization
    
    def set_audio_buffer_size(self, size):
        """
        Configura el tamaño del buffer de audio.
        
        Args:
            size: Tamaño del buffer en bytes (2048, 4096, 8192, etc.)
        """
        self.audio_buffer_size = size
        logger.info(f"Tamaño del buffer de audio configurado a {size} bytes")
        return self.audio_buffer_size
    
    def enhance_audio(self, input_path, callback=None):
        """
        Mejora la calidad del audio aplicando los ajustes actuales.
        Procesa el archivo en segundo plano y llama al callback cuando termina.
        
        Args:
            input_path: Ruta al archivo de audio a mejorar
            callback: Función a llamar cuando se complete el procesamiento
        """
        self.processing_queue.put((self._enhance_audio_impl, (input_path,), {}, callback))
    
    def _enhance_audio_impl(self, input_path):
        """
        Implementación real del proceso de mejora de audio.
        
        Args:
            input_path: Ruta al archivo de audio a mejorar
            
        Returns:
            Ruta al archivo de audio mejorado o el archivo original si falla
        """
        if not self.ffmpeg_path or not os.path.exists(self.ffmpeg_path):
            logger.warning("FFmpeg no disponible, no se puede mejorar el audio")
            return input_path
            
        if not os.path.exists(input_path):
            logger.error(f"Archivo no encontrado: {input_path}")
            return input_path
            
        try:
            # Crear un archivo temporal para la salida
            output_fd, output_path = tempfile.mkstemp(suffix='.mp3')
            os.close(output_fd)
            
            # Construir el filtro de FFmpeg para el ecualizador
            eq_filter = f"equalizer=f=60:width_type=o:width=2:g={self.eq_settings['bass']}"
            eq_filter += f",equalizer=f=1000:width_type=o:width=2:g={self.eq_settings['mid']}"
            eq_filter += f",equalizer=f=10000:width_type=o:width=2:g={self.eq_settings['treble']}"
            
            # Añadir normalización de volumen si está activada
            if self.volume_normalization:
                eq_filter += ",dynaudnorm=f=150:g=15:p=1:m=5"
            
            # Añadir ganancia general
            if self.eq_settings['gain'] != 0:
                eq_filter += f",volume={self.eq_settings['gain']}dB"
            
            # Construir el comando de FFmpeg
            cmd = [
                self.ffmpeg_path,
                '-y',
                '-i', input_path,
                '-af', eq_filter,
                '-c:a', 'libmp3lame',
                '-b:a', '320k',
                '-ar', '48000',
                output_path
            ]
            
            # Ejecutar FFmpeg
            creation_flags = 0
            if platform == 'win':
                creation_flags = subprocess.CREATE_NO_WINDOW
                
            logger.info(f"Mejorando audio: {input_path}")
            logger.debug(f"Comando FFmpeg: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=creation_flags
            )
            
            if result.returncode != 0:
                logger.error(f"Error al mejorar el audio: {result.stderr.decode()}")
                if os.path.exists(output_path):
                    os.remove(output_path)
                return input_path
                
            # Verificar que el archivo de salida existe y tiene tamaño
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logger.info(f"Audio mejorado correctamente: {output_path}")
                return output_path
            else:
                logger.error("El archivo de salida está vacío o no existe")
                return input_path
                
        except Exception as e:
            logger.error(f"Error al mejorar el audio: {e}")
            return input_path
