{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github"}, "source": ["# بناء تطبيق مشغل الموسيقى العربي - الإصدار المحدث v2\n", "\n", "هذا الإصدار المحدث يحل مشكلة `python-for-android` المفقود ويحسن عملية البناء."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_requirements", "cellView": "form"}, "outputs": [], "source": ["# @title تثبيت المتطلبات الأساسية وإعداد البيئة المحدث { display-mode: \"form\" }\n", "\n", "# تحديث النظام\n", "!apt-get update -qq\n", "\n", "# تثبيت Java 17 (مطلو<PERSON> للإصدارات الحديثة)\n", "!apt-get install -y openjdk-17-jdk\n", "\n", "# تعيين متغير JAVA_HOME\n", "import os\n", "os.environ['JAVA_HOME'] = '/usr/lib/jvm/java-17-openjdk-amd64'\n", "\n", "# تثبيت الأدوات الأساسية\n", "!apt-get install -y python3-pip build-essential git python3 python3-dev\n", "!apt-get install -y ffmpeg libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev libsdl2-ttf-dev\n", "!apt-get install -y libportmidi-dev libswscale-dev libavformat-dev libavcodec-dev zlib1g-dev\n", "!apt-get install -y autoconf automake libtool libltdl-dev libffi-dev ccache\n", "!apt-get install -y pkg-config libssl-dev\n", "\n", "# تثبيت Python packages بالترتيب الصحيح\n", "!pip install --upgrade pip setuptools wheel\n", "!pip install cython==0.29.33\n", "!pip install buildozer\n", "!pip install python-for-android\n", "\n", "# تثبيت متطلبات إضافية\n", "!pip install pillow requests\n", "\n", "print(\"✅ تم تثبيت جميع المتطلبات بنجاح!\")\n", "print(f\"Java Home: {os.environ.get('JAVA_HOME')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive", "cellView": "form"}, "outputs": [], "source": ["# @title ربط Google Drive { display-mode: \"form\" }\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "print(\"✅ تم ربط Google Drive بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "extract_project", "cellView": "form"}, "outputs": [], "source": ["# @title استخراج ملفات المشروع وتنظيفها { display-mode: \"form\" }\n", "import os\n", "import zipfile\n", "import shutil\n", "\n", "# مسار ملف ZIP في Google Drive\n", "zip_path = \"/content/drive/MyDrive/ArabicPlayer_Android_Only.zip\"\n", "\n", "# التحقق من وجود الملف\n", "if not os.path.exists(zip_path):\n", "    print(f\"❌ الملف غير موجود: {zip_path}\")\n", "    print(\"تأكد من رفع الملف إلى Google Drive أولاً\")\n", "else:\n", "    # إنشاء مجلد للمشروع\n", "    !rm -rf /content/app\n", "    !mkdir -p /content/app\n", "\n", "    # استخراج الملفات\n", "    with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "        zip_ref.extractall('/content/app')\n", "\n", "    # الانتقال إلى مجلد المشروع\n", "    %cd /content/app\n", "\n", "    # تنظيف الملفات غير الضرورية\n", "    !rm -rf __pycache__ downloads music temp_covers converted_audio album_art\n", "    !rm -f *.txt *.zip *.bak cover_*.jpg project.zip\n", "    !find . -name \"*.pyc\" -delete\n", "\n", "    # عرض محتويات المجلد\n", "    !ls -la\n", "    \n", "    print(\"✅ تم استخراج وتنظيف ملفات المشروع بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_android_sdk", "cellView": "form"}, "outputs": [], "source": ["# @title إعداد Android SDK وNDK { display-mode: \"form\" }\n", "\n", "# إنشاء مجلد Android\n", "!mkdir -p /root/.android\n", "\n", "# تعيين متغيرات البيئة\n", "import os\n", "os.environ['ANDROID_HOME'] = '/root/.buildozer/android/platform/android-sdk'\n", "os.environ['ANDROID_SDK_ROOT'] = '/root/.buildozer/android/platform/android-sdk'\n", "os.environ['ANDROIDAPI'] = '33'\n", "os.environ['ANDROIDMINAPI'] = '21'\n", "os.environ['ANDROIDNDK'] = '/root/.buildozer/android/platform/android-ndk-r25b'\n", "os.environ['NDKAPI'] = '21'\n", "\n", "# إنشاء مجلدات buildozer\n", "!mkdir -p /root/.buildozer/android/platform\n", "!mkdir -p /content/app/.buildozer/android/platform\n", "\n", "print(\"✅ تم إعداد Android SDK وNDK!\")\n", "print(f\"Android Home: {os.environ.get('ANDROID_HOME')}\")\n", "print(f\"Android NDK: {os.environ.get('ANDROIDNDK')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_buildozer_spec", "cellView": "form"}, "outputs": [], "source": ["# @title إنشاء ملف buildozer.spec المحدث { display-mode: \"form\" }\n", "\n", "buildozer_spec = '''[app]\n", "title = ArabicPlayer\n", "package.name = arabicplayer\n", "package.domain = org.arabicplayer\n", "source.dir = .\n", "source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json\n", "source.include_patterns = fonts/*.ttf, images/*.png, images/*.jpg, default_covers/*.jpg, *.py, *.kv, *.json\n", "source.exclude_dirs = __pycache__, bin, .buildozer, venv, downloads, temp_covers, converted_audio, album_art, music\n", "source.exclude_patterns = *.pyc, *.pyo, *.pyd, *.so, *.dll, *.egg-info, *.zip, *.part, *.bak, *.txt\n", "version = 1.0\n", "entrypoint = main.py\n", "icon.filename = images/default_album_cover.png\n", "presplash.filename = images/default_album_cover.png\n", "orientation = portrait\n", "fullscreen = 1\n", "\n", "requirements = python3,kivy==2.3.0,kivymd==1.1.1,mutagen,pyjnius,android,python-bidi,arabic-reshaper,pillow,ffpyplayer,requests,plyer\n", "\n", "[android]\n", "android.api = 33\n", "android.minapi = 21\n", "android.ndk = 25b\n", "android.ndk_api = 21\n", "android.sdk = 33\n", "android.skip_update = False\n", "android.accept_sdk_license = True\n", "android.archs = arm64-v8a,armeabi-v7a\n", "\n", "android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,<PERSON><PERSON>GE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS,RECORD_AUDIO\n", "android.allow_backup = True\n", "android.private_storage = True\n", "android.logcat_filters = *:S python:D\n", "android.entrypoint = org.kivy.android.PythonActivity\n", "android.apptheme = @android:style/Theme.NoTitleBar\n", "android.numeric_version = 1\n", "\n", "android.gradle_repositories = https://jitpack.io, https://maven.google.com/, https://jcenter.bintray.com/\n", "android.gradle_dependencies = com.google.android.material:material:1.4.0, androidx.core:core:1.6.0, androidx.appcompat:appcompat:1.3.1\n", "\n", "android.enable_androidx = True\n", "android.use_ccache = True\n", "\n", "[buildozer]\n", "log_level = 2\n", "warn_on_root = 0\n", "'''\n", "\n", "# كتابة ملف buildozer.spec\n", "with open('buildozer.spec', 'w') as f:\n", "    f.write(buildozer_spec)\n", "\n", "print(\"✅ تم إنشاء ملف buildozer.spec المحدث بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "initialize_buildozer", "cellView": "form"}, "outputs": [], "source": ["# @title تهيئة Buildozer وتحميل المتطلبات { display-mode: \"form\" }\n", "\n", "# تهيئة buildozer (تحميل SDK وNDK)\n", "print(\"🔄 بدء تهيئة Buildozer...\")\n", "!buildozer android debug --init-only\n", "\n", "print(\"✅ تم تهيئة Buildozer بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "build_apk", "cellView": "form"}, "outputs": [], "source": ["# @title بناء ملف APK { display-mode: \"form\" }\n", "\n", "# منع انقطاع الاتصال\n", "from IPython.display import display, Javascript\n", "display(Javascript('''\n", "function ClickConnect(){\n", "    console.log(\"Working\"); \n", "    document.querySelector(\"colab-toolbar-button#connect\").click() \n", "}\n", "setInterval(ClickConnect, 60000)\n", "'''))\n", "\n", "# بناء ملف APK مع حفظ السجلات\n", "print(\"🚀 بدء بناء APK...\")\n", "print(\"⏰ هذا قد يستغرق 20-40 دقيقة...\")\n", "\n", "!buildozer -v android debug > build_log.txt 2>&1\n", "\n", "# عرض نتيجة البناء\n", "print(\"\\n📋 آخر 50 سطر من سجل البناء:\")\n", "!tail -n 50 build_log.txt\n", "\n", "# التحقق من وجود APK\n", "import os\n", "apk_files = [f for f in os.listdir('bin') if f.endswith('.apk')] if os.path.exists('bin') else []\n", "\n", "if apk_files:\n", "    print(f\"\\n🎉 تم بناء APK بنجاح: {apk_files[0]}\")\n", "else:\n", "    print(\"\\n❌ فشل في بناء APK. تحقق من السجلات أعلاه.\")\n", "    print(\"\\n📋 عرض سجل الأخطاء:\")\n", "    !grep -i error build_log.txt | tail -n 10"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "copy_apk", "cellView": "form"}, "outputs": [], "source": ["# @title نسخ ملف APK إلى Google Drive { display-mode: \"form\" }\n", "import os\n", "import shutil\n", "from datetime import datetime\n", "\n", "# البحث عن ملف APK\n", "apk_path = None\n", "possible_paths = [\n", "    '/content/app/bin/arabicplayer-1.0-debug.apk',\n", "    '/content/app/bin/ArabicPlayer-1.0-debug.apk'\n", "]\n", "\n", "for path in possible_paths:\n", "    if os.path.exists(path):\n", "        apk_path = path\n", "        break\n", "\n", "# البحث في مج<PERSON>د bin عن أي ملف APK\n", "if not apk_path and os.path.exists('/content/app/bin'):\n", "    apk_files = [f for f in os.listdir('/content/app/bin') if f.endswith('.apk')]\n", "    if apk_files:\n", "        apk_path = os.path.join('/content/app/bin', apk_files[0])\n", "\n", "if apk_path and os.path.exists(apk_path):\n", "    # إنشاء مجلد في Google Drive\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_dir = f'/content/drive/MyDrive/ArabicPlayerAPK_{timestamp}'\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # نسخ ملف APK\n", "    apk_name = f'ArabicPlayer_{timestamp}.apk'\n", "    shutil.copy(apk_path, os.path.join(output_dir, apk_name))\n", "    \n", "    # نسخ سجلات البناء\n", "    if os.path.exists('build_log.txt'):\n", "        shutil.copy('build_log.txt', os.path.join(output_dir, 'build_log.txt'))\n", "    \n", "    # معلومات الملف\n", "    file_size = os.path.getsize(apk_path) / (1024 * 1024)  # بالميجابايت\n", "    \n", "    print(f\"🎉 تم نسخ ملف APK بنجاح!\")\n", "    print(f\"📁 المجلد: {output_dir}\")\n", "    print(f\"📱 اسم الملف: {apk_name}\")\n", "    print(f\"📊 حجم الملف: {file_size:.2f} MB\")\n", "    print(f\"\\n📋 تعليمات التثبيت:\")\n", "    print(f\"1. حمل الملف من Google Drive\")\n", "    print(f\"2. فعل 'مصادر غير معروفة' في إعدادات الأندرويد\")\n", "    print(f\"3. ثبت التطبيق واستمتع بالموسيقى العربية!\")\n", "    \n", "else:\n", "    print(\"❌ لم يتم العثور على ملف APK!\")\n", "    print(\"\\n🔍 البحث في مجلد bin:\")\n", "    if os.path.exists('/content/app/bin'):\n", "        !ls -la /content/app/bin/\n", "    else:\n", "        print(\"مج<PERSON><PERSON> bin غير موجود\")\n", "    \n", "    # نسخ سجلات البناء للتحقق من الأخطاء\n", "    if os.path.exists('build_log.txt'):\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        output_dir = f'/content/drive/MyDrive/ArabicPlayerAPK_Failed_{timestamp}'\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        shutil.copy('build_log.txt', os.path.join(output_dir, 'build_log.txt'))\n", "        print(f\"📋 تم نسخ سجلات البناء إلى: {output_dir}/build_log.txt\")"]}], "metadata": {"colab": {"name": "build_arabic_player_apk_fixed_v2.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}