#!/usr/bin/env python3
"""
ترقية التطبيق إلى النسخة الكاملة مع جميع المكتبات
Upgrade app to full version with all libraries
"""

import os
import shutil
import subprocess
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        logger.info(f"🔄 {description}: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            logger.error(f"❌ {description} failed")
            if result.stderr:
                logger.error(f"Error: {result.stderr[:500]}...")
            return False, result.stderr
    except Exception as e:
        logger.error(f"❌ Exception in {description}: {e}")
        return False, str(e)

def backup_current_version():
    """إنشاء نسخة احتياطية من النسخة الحالية"""
    logger.info("🔄 Creating backup of current version...")
    
    backup_dir = "backup_simple_version"
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'main.py',
        'buildozer.spec',
        'MusicPlayer.kv'
    ]
    
    for file_name in files_to_backup:
        if os.path.exists(file_name):
            try:
                shutil.copy(file_name, os.path.join(backup_dir, file_name))
                logger.info(f"✅ Backed up: {file_name}")
            except Exception as e:
                logger.error(f"❌ Failed to backup {file_name}: {e}")

def upgrade_buildozer_spec():
    """ترقية ملف buildozer.spec"""
    logger.info("🔄 Upgrading buildozer.spec...")
    
    if os.path.exists('buildozer_full_compatible.spec'):
        try:
            shutil.copy('buildozer_full_compatible.spec', 'buildozer.spec')
            logger.info("✅ Updated buildozer.spec with full libraries")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to update buildozer.spec: {e}")
            return False
    else:
        logger.error("❌ buildozer_full_compatible.spec not found")
        return False

def check_required_files():
    """فحص الملفات المطلوبة"""
    logger.info("🔍 Checking required files...")
    
    required_files = [
        'main.py',
        'MusicPlayer.kv',
        'buildozer_full_compatible.spec'
    ]
    
    missing_files = []
    for file_name in required_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
    
    if missing_files:
        logger.error(f"❌ Missing required files: {missing_files}")
        return False
    else:
        logger.info("✅ All required files found")
        return True

def test_imports():
    """اختبار استيراد المكتبات"""
    logger.info("🧪 Testing library imports...")
    
    test_script = """
import sys
import logging
logging.basicConfig(level=logging.INFO)

# Test basic imports
try:
    import kivy
    print("✅ Kivy imported successfully")
except ImportError as e:
    print(f"❌ Kivy import failed: {e}")
    sys.exit(1)

try:
    import kivymd
    print("✅ KivyMD imported successfully")
except ImportError as e:
    print(f"❌ KivyMD import failed: {e}")
    sys.exit(1)

try:
    import mutagen
    print("✅ Mutagen imported successfully")
except ImportError as e:
    print(f"❌ Mutagen import failed: {e}")

try:
    import requests
    print("✅ Requests imported successfully")
except ImportError as e:
    print(f"❌ Requests import failed: {e}")

try:
    import arabic_reshaper
    import bidi
    print("✅ Arabic text libraries imported successfully")
except ImportError as e:
    print(f"❌ Arabic text libraries import failed: {e}")

try:
    import plyer
    print("✅ Plyer imported successfully")
except ImportError as e:
    print(f"❌ Plyer import failed: {e}")

print("🎉 Import test completed")
"""
    
    try:
        with open('test_imports.py', 'w') as f:
            f.write(test_script)
        
        success, output = run_command("python test_imports.py", "Testing imports")
        
        # Clean up
        if os.path.exists('test_imports.py'):
            os.remove('test_imports.py')
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error testing imports: {e}")
        return False

def clean_and_build():
    """تنظيف وبناء النسخة الكاملة"""
    logger.info("🏗️ Building full version...")
    
    # Clean previous builds
    logger.info("🧹 Cleaning previous builds...")
    
    if os.path.exists('.buildozer'):
        try:
            shutil.rmtree('.buildozer')
            logger.info("✅ Removed .buildozer directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove .buildozer: {e}")
    
    if os.path.exists('bin'):
        try:
            shutil.rmtree('bin')
            logger.info("✅ Removed bin directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove bin: {e}")
    
    # Run buildozer clean
    run_command("buildozer android clean", "Buildozer clean")
    
    # Build APK
    logger.info("🔨 Building APK with full libraries...")
    success, output = run_command("buildozer android debug", "Building full APK")
    
    if success:
        logger.info("🎉 Full version APK built successfully!")
        
        # Check for APK files
        apk_files = []
        if os.path.exists('bin'):
            for file in os.listdir('bin'):
                if file.endswith('.apk'):
                    apk_files.append(os.path.join('bin', file))
        
        if apk_files:
            for apk in apk_files:
                size = os.path.getsize(apk) / (1024 * 1024)  # MB
                logger.info(f"📱 APK created: {apk} ({size:.2f} MB)")
        
        return True
    else:
        logger.error("❌ Full version build failed")
        return False

def show_upgrade_summary():
    """عرض ملخص الترقية"""
    print("\n" + "=" * 60)
    print("🎯 UPGRADE TO FULL VERSION SUMMARY")
    print("=" * 60)
    
    print("\n📦 Added Libraries:")
    print("   ✅ requests - for online features")
    print("   ✅ urllib3 - for HTTP requests")
    print("   ✅ plyer - for device features")
    print("   ✅ pillow - for image processing")
    print("   ✅ arabic-reshaper & python-bidi - for Arabic text")
    print("   ✅ certifi - for SSL certificates")
    
    print("\n🔧 Enhanced Features:")
    print("   ✅ Online music search and streaming")
    print("   ✅ Download manager for offline playback")
    print("   ✅ Audio enhancer with equalizer")
    print("   ✅ Device compatibility detection")
    print("   ✅ Adaptive memory management")
    print("   ✅ Notification support")
    print("   ✅ Vibration feedback")
    
    print("\n📱 Device Compatibility:")
    print("   ✅ Android API 19+ (Android 4.4+)")
    print("   ✅ Support for old devices (2GB RAM+)")
    print("   ✅ Support for new devices (full features)")
    print("   ✅ Adaptive performance based on device")
    
    print("\n🎯 Next Steps:")
    print("   1. Test the APK on different devices")
    print("   2. Check all features work correctly")
    print("   3. Monitor performance on old devices")
    print("   4. Add more features gradually if needed")

def main():
    """الدالة الرئيسية"""
    print("🚀 Starting upgrade to full version...")
    
    # Step 1: Check required files
    print("\n📋 Step 1: Checking required files...")
    if not check_required_files():
        print("❌ Missing required files. Cannot proceed.")
        return
    
    # Step 2: Create backup
    print("\n📋 Step 2: Creating backup...")
    backup_current_version()
    
    # Step 3: Test imports (optional)
    print("\n📋 Step 3: Testing library imports...")
    test_imports()  # Continue even if some imports fail
    
    # Step 4: Upgrade buildozer.spec
    print("\n📋 Step 4: Upgrading buildozer.spec...")
    if not upgrade_buildozer_spec():
        print("❌ Failed to upgrade buildozer.spec")
        return
    
    # Step 5: Build full version
    print("\n📋 Step 5: Building full version...")
    success = clean_and_build()
    
    if success:
        show_upgrade_summary()
        print("\n🎉 SUCCESS! Full version APK created!")
        print("📱 Check the 'bin' folder for your enhanced APK")
        print("🚀 Ready for testing on Android devices!")
    else:
        print("\n❌ Build failed. Check logs for details.")
        print("💡 You can restore the simple version from 'backup_simple_version' folder")

if __name__ == "__main__":
    main()
