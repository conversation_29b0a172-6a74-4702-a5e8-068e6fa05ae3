[app]

# (str) Title of your application
title = ArabicPlayer

# (str) Package name
package.name = arabicplayer

# (str) Package domain (needed for android/ios packaging)
package.domain = org.arabicplayer

# (str) Source code where the main.py live
source.dir = .

# (list) Source files to include (let empty to include all the files)
source.include_exts = py

# (list) List of inclusions using pattern matching
source.include_patterns = *.py

# (list) Source files to exclude (let empty to not exclude anything)
source.exclude_exts = spec

# (list) List of directory to exclude (let empty to not exclude anything)
source.exclude_dirs = tests,bin,.buildozer,venv,__pycache__

# (list) List of exclusions using pattern matching
source.exclude_patterns = license,*.pyc,*.pyo

# (str) Application versioning (method 1)
version = 1.0

# (str) Application entrypoint
entrypoint = main.py

# (str) Supported orientation (landscape, sensorLandscape, portrait or all)
orientation = portrait

# (bool) Indicate if the application should be fullscreen or not
fullscreen = 0

# (list) Application requirements - MINIMAL WORKING SET
requirements = python<PERSON>,kivy,py<PERSON><PERSON>,android

# (list) Permissions - minimal set
android.permissions = INTERNET

# (int) Target Android API - using stable version
android.api = 31

# (int) Minimum API your APK / AAB will support.
android.minapi = 21

# (int) Android SDK version to use
android.sdk = 31

# (str) Android NDK version to use
android.ndk = 25b

# (int) Android NDK API to use
android.ndk_api = 21

# (bool) Use --private data storage (True) or --dir public storage (False)
android.private_storage = True

# (bool) If True, then skip trying to update the Android sdk
# Set to False to allow downloading build tools
android.skip_update = False

# (bool) If True, then automatically accept SDK license
android.accept_sdk_license = True

# (str) Android entry point
android.entrypoint = org.kivy.android.PythonActivity

# (str) Path to the Android logcat filters to use
android.logcat_filters = *:S python:D

# (str) The Android arch to build for - single arch for testing
android.archs = arm64-v8a

# (int) overrides automatic versionCode computation
android.numeric_version = 1

# (str) Android build tools version to use
# This should help with the build-tools issue
android.build_tools = 31.0.0

# (str) Android SDK directory (if empty, it will be automatically downloaded.)
#android.sdk_path =

# (str) Android NDK directory (if empty, it will be automatically downloaded.)
#android.ndk_path =

[buildozer]

# (int) Log level (0 = error only, 1 = info, 2 = debug (with command output))
log_level = 2

# (int) Display warning if buildozer is run as root (0 = False, 1 = True)
warn_on_root = 0

# (str) Path to build artifact storage, absolute or relative to spec file
build_dir = ./.buildozer

# (str) Path to build output (i.e. .apk, .aab, .ipa) storage
bin_dir = ./bin
