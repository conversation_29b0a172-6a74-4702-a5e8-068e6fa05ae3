# دليل استكشاف الأخطاء وإصلاحها للتطبيق على الأندرويد

## 🔍 خطوات تشخيص المشكلة

### 1. فحص logcat للأخطاء
```bash
# تشغيل logcat لمراقبة الأخطاء
adb logcat | grep python

# أو حفظ السجل في ملف
adb logcat > app_log.txt
```

### 2. البحث عن رسائل الخطأ الشائعة
```
ImportError: No module named 'module_name'
ModuleNotFoundError: No module named 'module_name'
AttributeError: module 'module_name' has no attribute 'attribute_name'
FileNotFoundError: [Errno 2] No such file or directory
```

## 🛠️ حلول للمشاكل الشائعة

### مشكلة 1: ImportError للمكتبات
**الخطأ:**
```
ImportError: No module named 'pytube'
```

**الحل:**
1. تأكد من إضافة المكتبة في buildozer.spec
2. نظف وأعد بناء المشروع:
```bash
buildozer android clean
buildozer android debug
```

### مشكلة 2: مشاكل الصوت
**الخطأ:**
```
[CRITICAL] [Audio] Unable to find a loader for this audio format
```

**الحل:**
1. تأكد من وجود pygame و ffpyplayer في المتطلبات
2. أضف إعدادات الصوت في main.py:
```python
os.environ['KIVY_AUDIO'] = 'pygame'
os.environ['SDL_AUDIODRIVER'] = 'android'
```

### مشكلة 3: مشاكل النص العربي
**الخطأ:**
```
ModuleNotFoundError: No module named 'arabic_reshaper'
```

**الحل:**
1. أضف المكتبات العربية:
```
python-bidi==0.4.2
arabic-reshaper==3.0.0
```

### مشكلة 4: مشاكل الشبكة
**الخطأ:**
```
requests.exceptions.SSLError
```

**الحل:**
1. أضف certifi للشهادات:
```
certifi
urllib3
```

## 📱 اختبار تدريجي

### المرحلة 1: التطبيق الأساسي
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,pyjnius,android
```

### المرحلة 2: إضافة الصوت
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,pyjnius,android,mutagen==1.47.0,pygame
```

### المرحلة 3: إضافة النص العربي
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,pyjnius,android,mutagen==1.47.0,pygame,python-bidi==0.4.2,arabic-reshaper==3.0.0
```

### المرحلة 4: إضافة الشبكة
```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,pyjnius,android,mutagen==1.47.0,pygame,python-bidi==0.4.2,arabic-reshaper==3.0.0,requests==2.31.0,certifi
```

## 🔧 إصلاحات في الكود

### 1. إضافة معالجة أخطاء للاستيراد
```python
# في بداية main.py
try:
    from pytube import YouTube
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False
    print("Warning: pytube not available")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("Warning: psutil not available")
```

### 2. تحسين إعدادات الصوت
```python
# في main.py قبل استيراد kivy
import os
os.environ['KIVY_AUDIO'] = 'pygame'
os.environ['SDL_AUDIODRIVER'] = 'android'
os.environ['KIVY_AUDIO_BUFFER_SIZE'] = '4096'
```

### 3. إضافة معالجة أخطاء للملفات
```python
def safe_load_image(image_path):
    try:
        if os.path.exists(image_path):
            return image_path
        else:
            return "images/default_album_cover.png"
    except Exception as e:
        print(f"Error loading image: {e}")
        return "images/default_album_cover.png"
```

## 📋 قائمة فحص قبل البناء

### ✅ ملفات مطلوبة
- [ ] main.py موجود
- [ ] جميع ملفات .kv موجودة
- [ ] مجلد fonts يحتوي على الخط العربي
- [ ] مجلد images يحتوي على الصور الافتراضية
- [ ] buildozer.spec محدث بجميع المكتبات

### ✅ إعدادات buildozer.spec
- [ ] requirements تحتوي على جميع المكتبات
- [ ] android.permissions تحتوي على جميع الأذونات
- [ ] android.api = 34
- [ ] android.ndk = 25b
- [ ] android.archs = armeabi-v7a, arm64-v8a

### ✅ إعدادات الكود
- [ ] معالجة أخطاء للاستيراد
- [ ] إعدادات الصوت للأندرويد
- [ ] مسارات الملفات صحيحة

## 🚨 علامات تحذيرية

### إذا رأيت هذه الأخطاء:
1. **"No module named"** → مكتبة مفقودة من requirements
2. **"Permission denied"** → إذن مفقود من android.permissions
3. **"File not found"** → ملف مفقود من source.include_patterns
4. **"SSL Error"** → مشكلة في شهادات الأمان (أضف certifi)
5. **"Audio format not supported"** → مشكلة في مكتبات الصوت

## 🎯 خطة الإصلاح المرحلية

### الخطوة 1: بناء نسخة أساسية
```bash
# استخدم buildozer.spec مبسط
buildozer android clean
buildozer android debug
```

### الخطوة 2: اختبار النسخة الأساسية
```bash
adb install bin/arabicplayer-1.0-debug.apk
adb logcat | grep python
```

### الخطوة 3: إضافة المكتبات تدريجيًا
- أضف مكتبة واحدة في كل مرة
- اختبر بعد كل إضافة
- راقب logcat للأخطاء

### الخطوة 4: إصلاح الأخطاء
- حدد المكتبة المسببة للمشكلة
- ابحث عن بدائل إذا لزم الأمر
- أضف معالجة أخطاء في الكود

## 📞 طلب المساعدة

إذا استمرت المشاكل، قدم المعلومات التالية:
1. رسالة الخطأ الكاملة من logcat
2. ملف buildozer.spec المستخدم
3. إصدار الأندرويد المستخدم للاختبار
4. خطوات إعادة إنتاج المشكلة

## 🔄 أوامر مفيدة

```bash
# تنظيف المشروع
buildozer android clean

# بناء APK للتطوير
buildozer android debug

# بناء APK للإنتاج
buildozer android release

# عرض الأجهزة المتصلة
adb devices

# تثبيت APK
adb install bin/arabicplayer-1.0-debug.apk

# إلغاء تثبيت التطبيق
adb uninstall org.arabicplayer

# مراقبة السجلات
adb logcat | grep python
```
