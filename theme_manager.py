"""
Theme Manager for Music Player Application
Provides a comprehensive theme system with multiple color options
"""

from kivy.properties import DictProperty, StringProperty, ColorProperty, ObjectProperty
from kivy.event import EventDispatcher
from kivy.utils import get_color_from_hex
from kivy.metrics import dp
from kivy.clock import Clock

# Theme definitions with color palettes
THEMES = {
    "Blue": {
        "primary": "#2196F3",
        "primary_dark": "#1976D2",
        "primary_light": "#BBDEFB",
        "accent": "#FF4081",
        "background_light": "#F5F5F5",
        "background_dark": "#121212",
        "card_light": "#FFFFFF",
        "card_dark": "#1E1E1E",
        "text_light": "#212121",
        "text_dark": "#FFFFFF",
        "error": "#F44336",
        "success": "#4CAF50",
        "warning": "#FFC107",
        "info": "#2196F3",
    },
    "Purple": {
        "primary": "#673AB7",
        "primary_dark": "#512DA8",
        "primary_light": "#D1C4E9",
        "accent": "#FFC107",
        "background_light": "#F5F5F5",
        "background_dark": "#121212",
        "card_light": "#FFFFFF",
        "card_dark": "#1E1E1E",
        "text_light": "#212121",
        "text_dark": "#FFFFFF",
        "error": "#F44336",
        "success": "#4CAF50",
        "warning": "#FFC107",
        "info": "#2196F3",
    },
    "Green": {
        "primary": "#4CAF50",
        "primary_dark": "#388E3C",
        "primary_light": "#C8E6C9",
        "accent": "#FF5722",
        "background_light": "#F5F5F5",
        "background_dark": "#121212",
        "card_light": "#FFFFFF",
        "card_dark": "#1E1E1E",
        "text_light": "#212121",
        "text_dark": "#FFFFFF",
        "error": "#F44336",
        "success": "#4CAF50",
        "warning": "#FFC107",
        "info": "#2196F3",
    },
    "Orange": {
        "primary": "#FF9800",
        "primary_dark": "#F57C00",
        "primary_light": "#FFE0B2",
        "accent": "#607D8B",
        "background_light": "#F5F5F5",
        "background_dark": "#121212",
        "card_light": "#FFFFFF",
        "card_dark": "#1E1E1E",
        "text_light": "#212121",
        "text_dark": "#FFFFFF",
        "error": "#F44336",
        "success": "#4CAF50",
        "warning": "#FFC107",
        "info": "#2196F3",
    },
    "Red": {
        "primary": "#F44336",
        "primary_dark": "#D32F2F",
        "primary_light": "#FFCDD2",
        "accent": "#4CAF50",
        "background_light": "#F5F5F5",
        "background_dark": "#121212",
        "card_light": "#FFFFFF",
        "card_dark": "#1E1E1E",
        "text_light": "#212121",
        "text_dark": "#FFFFFF",
        "error": "#F44336",
        "success": "#4CAF50",
        "warning": "#FFC107",
        "info": "#2196F3",
    },
}

class ThemeManager(EventDispatcher):
    """Manages application themes and provides color values"""
    
    # Current theme properties
    current_theme = StringProperty("Blue")
    theme_style = StringProperty("Light")  # "Light" or "Dark"
    colors = DictProperty({})
    
    # Convenience properties for common colors
    primary_color = ColorProperty([0, 0, 0, 1])
    accent_color = ColorProperty([0, 0, 0, 1])
    background_color = ColorProperty([0, 0, 0, 1])
    card_color = ColorProperty([0, 0, 0, 1])
    text_color = ColorProperty([0, 0, 0, 1])
    
    def __init__(self, **kwargs):
        super(ThemeManager, self).__init__(**kwargs)
        
        # Initialize with default theme
        self.set_theme("Blue", "Light")
        
        # Bind to property changes
        self.bind(current_theme=self.update_colors)
        self.bind(theme_style=self.update_colors)
    
    def set_theme(self, theme_name, theme_style=None):
        """Set the current theme and optionally the theme style"""
        if theme_name in THEMES:
            self.current_theme = theme_name
        
        if theme_style in ["Light", "Dark"]:
            self.theme_style = theme_style
    
    def toggle_theme_style(self):
        """Toggle between light and dark theme styles"""
        self.theme_style = "Dark" if self.theme_style == "Light" else "Light"
    
    def cycle_theme(self):
        """Cycle to the next available theme"""
        theme_names = list(THEMES.keys())
        current_index = theme_names.index(self.current_theme)
        next_index = (current_index + 1) % len(theme_names)
        self.current_theme = theme_names[next_index]
    
    def update_colors(self, *args):
        """Update color properties based on current theme and style"""
        if self.current_theme not in THEMES:
            return
        
        theme = THEMES[self.current_theme]
        is_dark = self.theme_style == "Dark"
        
        # Update colors dictionary
        self.colors = {
            "primary": get_color_from_hex(theme["primary"]),
            "primary_dark": get_color_from_hex(theme["primary_dark"]),
            "primary_light": get_color_from_hex(theme["primary_light"]),
            "accent": get_color_from_hex(theme["accent"]),
            "background": get_color_from_hex(theme["background_dark"] if is_dark else theme["background_light"]),
            "card": get_color_from_hex(theme["card_dark"] if is_dark else theme["card_light"]),
            "text": get_color_from_hex(theme["text_dark"] if is_dark else theme["text_light"]),
            "error": get_color_from_hex(theme["error"]),
            "success": get_color_from_hex(theme["success"]),
            "warning": get_color_from_hex(theme["warning"]),
            "info": get_color_from_hex(theme["info"]),
        }
        
        # Update convenience properties
        self.primary_color = self.colors["primary"]
        self.accent_color = self.colors["accent"]
        self.background_color = self.colors["background"]
        self.card_color = self.colors["card"]
        self.text_color = self.colors["text"]
        
        # Dispatch event for theme change
        self.dispatch("on_theme_changed")
    
    def on_theme_changed(self, *args):
        """Event dispatched when theme changes"""
        pass
    
    def get_color(self, color_name):
        """Get a specific color from the current theme"""
        return self.colors.get(color_name, [0, 0, 0, 1])
