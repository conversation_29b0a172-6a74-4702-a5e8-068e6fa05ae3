"""
نسخة مبسطة من التطبيق للاختبار
تحتوي على الحد الأدنى من الكود للتأكد من أن البناء يعمل
"""

import os
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إعدادات Kivy الأساسية
from kivy.config import Config
Config.set('kivy', 'log_level', 'info')

# استيراد Kivy
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.clock import Clock

# استيراد KivyMD
try:
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.label import MDLabel
    from kivymd.uix.button import MDRaisedButton
    KIVYMD_AVAILABLE = True
    logger.info("KivyMD available")
except ImportError as e:
    logger.warning(f"KivyMD not available: {e}")
    KIVYMD_AVAILABLE = False

class SimpleTestWidget(BoxLayout):
    """واجهة بسيطة للاختبار"""
    
    def __init__(self, **kwargs):
        super(SimpleTestWidget, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # إضافة تسمية ترحيب
        welcome_label = Label(
            text='مرحباً بك في مشغل الموسيقى العربي\nArabic Music Player',
            font_size='20sp',
            halign='center',
            valign='middle'
        )
        welcome_label.bind(size=welcome_label.setter('text_size'))
        self.add_widget(welcome_label)
        
        # إضافة تسمية حالة
        self.status_label = Label(
            text='التطبيق يعمل بنجاح!\nApp is running successfully!',
            font_size='16sp',
            halign='center',
            valign='middle'
        )
        self.status_label.bind(size=self.status_label.setter('text_size'))
        self.add_widget(self.status_label)
        
        # إضافة زر اختبار
        test_button = Button(
            text='اختبار / Test',
            size_hint=(1, None),
            height='50dp'
        )
        test_button.bind(on_press=self.on_test_button)
        self.add_widget(test_button)
        
        # جدولة تحديث الحالة
        Clock.schedule_interval(self.update_status, 2.0)
        
    def on_test_button(self, instance):
        """معالج زر الاختبار"""
        self.status_label.text = 'تم الضغط على الزر!\nButton pressed!'
        logger.info("Test button pressed")
        
        # إعادة تعيين النص بعد ثانيتين
        Clock.schedule_once(self.reset_status, 2.0)
        
    def reset_status(self, dt):
        """إعادة تعيين نص الحالة"""
        self.status_label.text = 'التطبيق يعمل بنجاح!\nApp is running successfully!'
        
    def update_status(self, dt):
        """تحديث حالة التطبيق"""
        import time
        current_time = time.strftime('%H:%M:%S')
        self.status_label.text = f'التطبيق يعمل بنجاح!\nApp is running successfully!\nTime: {current_time}'

class SimpleMDTestWidget(MDBoxLayout):
    """واجهة بسيطة باستخدام KivyMD"""
    
    def __init__(self, **kwargs):
        super(SimpleMDTestWidget, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # إضافة تسمية ترحيب
        welcome_label = MDLabel(
            text='مرحباً بك في مشغل الموسيقى العربي\nArabic Music Player',
            font_style='H5',
            halign='center',
            theme_text_color='Primary'
        )
        self.add_widget(welcome_label)
        
        # إضافة تسمية حالة
        self.status_label = MDLabel(
            text='التطبيق يعمل بنجاح!\nApp is running successfully!',
            font_style='Body1',
            halign='center',
            theme_text_color='Secondary'
        )
        self.add_widget(self.status_label)
        
        # إضافة زر اختبار
        test_button = MDRaisedButton(
            text='اختبار / Test',
            size_hint=(1, None),
            height='50dp'
        )
        test_button.bind(on_press=self.on_test_button)
        self.add_widget(test_button)
        
        # جدولة تحديث الحالة
        Clock.schedule_interval(self.update_status, 2.0)
        
    def on_test_button(self, instance):
        """معالج زر الاختبار"""
        self.status_label.text = 'تم الضغط على الزر!\nButton pressed!'
        logger.info("Test button pressed")
        
        # إعادة تعيين النص بعد ثانيتين
        Clock.schedule_once(self.reset_status, 2.0)
        
    def reset_status(self, dt):
        """إعادة تعيين نص الحالة"""
        self.status_label.text = 'التطبيق يعمل بنجاح!\nApp is running successfully!'
        
    def update_status(self, dt):
        """تحديث حالة التطبيق"""
        import time
        current_time = time.strftime('%H:%M:%S')
        self.status_label.text = f'التطبيق يعمل بنجاح!\nApp is running successfully!\nTime: {current_time}'

class SimpleTestApp(App):
    """تطبيق بسيط للاختبار باستخدام Kivy العادي"""
    
    def build(self):
        logger.info("Building Simple Test App with Kivy")
        return SimpleTestWidget()

class SimpleMDTestApp(MDApp):
    """تطبيق بسيط للاختبار باستخدام KivyMD"""
    
    def build(self):
        logger.info("Building Simple Test App with KivyMD")
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
        return SimpleMDTestWidget()

def main():
    """الدالة الرئيسية"""
    logger.info("Starting Arabic Music Player Test App")
    
    try:
        if KIVYMD_AVAILABLE:
            logger.info("Using KivyMD")
            app = SimpleMDTestApp()
        else:
            logger.info("Using Kivy")
            app = SimpleTestApp()
            
        app.run()
        
    except Exception as e:
        logger.error(f"Error running app: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == '__main__':
    main()
