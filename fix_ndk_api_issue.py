#!/usr/bin/env python3
"""
إصلاح مشكلة NDK API مع Python 3.11
Fix NDK API issue with Python 3.11
"""

import os
import shutil
import subprocess
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        logger.info(f"🔄 {description}: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            logger.error(f"❌ {description} failed")
            if result.stderr:
                logger.error(f"Error: {result.stderr[:500]}...")
            return False, result.stderr
    except Exception as e:
        logger.error(f"❌ Exception in {description}: {e}")
        return False, str(e)

def fix_ndk_api_compatibility():
    """إصلاح مشكلة توافق NDK API"""
    logger.info("🔧 Fixing NDK API compatibility issue...")
    
    print("=" * 60)
    print("🎯 NDK API Fix: Python 3.11 requires NDK API 21+")
    print("=" * 60)
    
    # نسخ الإعدادات المحدثة
    if os.path.exists('buildozer_compatible_fixed.spec'):
        try:
            # نسخ احتياطية من buildozer.spec الحالي
            if os.path.exists('buildozer.spec'):
                shutil.copy('buildozer.spec', 'buildozer_backup_api19.spec')
                logger.info("✅ Created backup of current buildozer.spec")
            
            shutil.copy('buildozer_compatible_fixed.spec', 'buildozer.spec')
            logger.info("✅ Updated buildozer.spec with NDK API 21")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to update buildozer.spec: {e}")
            return False
    else:
        logger.error("❌ buildozer_compatible_fixed.spec not found")
        return False

def show_compatibility_info():
    """عرض معلومات التوافق"""
    print("\n📱 Device Compatibility After Fix:")
    print("=" * 50)
    print("✅ Android 5.0+ (API 21+) - SUPPORTED")
    print("✅ Android 6.0+ (API 23+) - FULLY SUPPORTED")
    print("✅ Android 7.0+ (API 24+) - ALL FEATURES")
    print("✅ Android 8.0+ (API 26+) - OPTIMAL PERFORMANCE")
    print()
    print("❌ Android 4.4 (API 19-20) - NOT SUPPORTED")
    print("   (Python 3.11 limitation)")
    print()
    print("📊 Market Coverage:")
    print("   ✅ ~95% of active Android devices")
    print("   ✅ All devices from 2014 onwards")
    print("   ✅ Excellent compatibility")

def clean_and_build():
    """تنظيف وبناء APK"""
    logger.info("🧹 Cleaning and building APK...")
    
    # تنظيف البيئة
    logger.info("🔄 Cleaning buildozer environment...")
    
    # حذف مجلد .buildozer
    if os.path.exists('.buildozer'):
        try:
            shutil.rmtree('.buildozer')
            logger.info("✅ Removed .buildozer directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove .buildozer: {e}")
    
    # حذف مجلد bin
    if os.path.exists('bin'):
        try:
            shutil.rmtree('bin')
            logger.info("✅ Removed bin directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove bin: {e}")
    
    # تشغيل buildozer clean
    success, output = run_command("buildozer android clean", "Buildozer clean")
    
    # بناء APK
    logger.info("🏗️ Building APK with NDK API 21...")
    success, output = run_command("buildozer android debug", "Building APK")
    
    if success:
        logger.info("🎉 APK built successfully!")
        
        # البحث عن ملف APK
        apk_files = []
        if os.path.exists('bin'):
            for file in os.listdir('bin'):
                if file.endswith('.apk'):
                    apk_files.append(os.path.join('bin', file))
        
        if apk_files:
            for apk in apk_files:
                size = os.path.getsize(apk) / (1024 * 1024)  # MB
                logger.info(f"📱 APK created: {apk} ({size:.2f} MB)")
        
        return True
    else:
        logger.error("❌ APK build failed")
        return False

def create_alternative_version():
    """إنشاء نسخة بديلة للأجهزة الأقدم"""
    logger.info("🔄 Creating alternative version for older devices...")
    
    # إنشاء نسخة بـ Python 3.9 للأجهزة الأقدم
    alternative_spec = """[app]
title = ArabicPlayer
package.name = arabicplayer
package.domain = org.arabicplayer
source.dir = .
source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json
source.exclude_dirs = tests,bin,.buildozer,venv,__pycache__
version = 1.0
entrypoint = main.py
orientation = portrait
fullscreen = 0

# ALTERNATIVE VERSION FOR OLDER DEVICES (Android 4.4+)
requirements = python3==3.9.17,kivy==2.2.0,kivymd==1.0.2,mutagen==1.47.0,pyjnius,android

android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET
android.api = 28
android.minapi = 19
android.sdk = 28
android.ndk = 23c
android.ndk_api = 19
android.private_storage = True
android.skip_update = False
android.accept_sdk_license = True
android.entrypoint = org.kivy.android.PythonActivity
android.archs = arm64-v8a

[buildozer]
log_level = 2
warn_on_root = 0
"""
    
    try:
        with open('buildozer_old_devices.spec', 'w') as f:
            f.write(alternative_spec)
        logger.info("✅ Created alternative spec for older devices")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create alternative spec: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 Starting NDK API compatibility fix...")
    print("🎯 Issue: Python 3.11 requires NDK API 21+, but config uses API 19")
    print()
    
    # الخطوة 1: إصلاح مشكلة NDK API
    print("📋 Step 1: Fixing NDK API compatibility...")
    if not fix_ndk_api_compatibility():
        print("❌ Failed to fix NDK API issue")
        return
    
    # الخطوة 2: عرض معلومات التوافق
    print("\n📋 Step 2: Device compatibility information...")
    show_compatibility_info()
    
    # الخطوة 3: إنشاء نسخة بديلة للأجهزة الأقدم
    print("\n📋 Step 3: Creating alternative version for older devices...")
    create_alternative_version()
    
    # الخطوة 4: تنظيف وبناء
    print("\n📋 Step 4: Cleaning and building APK...")
    success = clean_and_build()
    
    if success:
        print("\n🎉 SUCCESS! APK created successfully!")
        print("📱 Check the 'bin' folder for your APK file")
        print("🚀 Compatible with Android 5.0+ devices!")
        print("\n📋 What was fixed:")
        print("   ✅ Updated NDK API from 19 to 21")
        print("   ✅ Updated minapi from 19 to 21")
        print("   ✅ Compatible with Python 3.11")
        print("   ✅ Supports 95% of active Android devices")
        print("\n💡 For Android 4.4 support:")
        print("   Use: buildozer_old_devices.spec")
        print("   (Python 3.9 + older libraries)")
    else:
        print("\n❌ Build failed. Possible solutions:")
        print("1. Check internet connection for downloading NDK")
        print("2. Try with fewer libraries:")
        print("   requirements = python3,kivy,kivymd,pyjnius,android")
        print("3. Use the alternative spec for older devices:")
        print("   cp buildozer_old_devices.spec buildozer.spec")

if __name__ == "__main__":
    main()
