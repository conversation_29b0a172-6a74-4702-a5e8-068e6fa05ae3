"""
نسخة مبسطة من مشغل الموسيقى العربي للاختبار مع Gradle
تحتوي على الحد الأدنى من الكود لتجنب مشاكل Gradle
"""

import os
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === SAFE AUDIO SETUP ===
def setup_audio_safely():
    """إعداد الصوت بشكل آمن للأندرويد"""
    try:
        os.environ['KIVY_AUDIO'] = 'pygame'
        os.environ['SDL_AUDIODRIVER'] = 'android'
        logger.info("✅ Audio provider set to pygame for Android")
        return True
    except Exception as e:
        logger.warning(f"Failed to set pygame audio: {e}")
        return False

# تشغيل إعداد الصوت
audio_setup_success = setup_audio_safely()

# إعداد النص العربي
os.environ['KIVY_TEXT'] = 'pil'

from kivy.config import Config
from kivy.utils import platform

# Set audio provider in the 'kivy' section
if not Config.has_section('kivy'):
    Config.add_section('kivy')

if audio_setup_success:
    try:
        Config.set('kivy', 'audio', 'pygame')
        logger.info("✅ Kivy audio provider set to: pygame for Android")
    except Exception as e:
        logger.warning(f"Failed to set audio provider 'pygame' in config: {e}")

# === KIVY IMPORTS ===
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.clock import Clock
from kivy.core.window import Window

# === KIVYMD IMPORTS ===
try:
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.label import MDLabel
    from kivymd.uix.button import MDRaisedButton
    KIVYMD_AVAILABLE = True
    logger.info("KivyMD imported successfully")
except ImportError as e:
    KIVYMD_AVAILABLE = False
    logger.error(f"KivyMD not available: {e}")
    # يجب إيقاف التطبيق إذا لم تكن KivyMD متاحة
    raise ImportError("KivyMD is required for this application")

# === ANDROID PERMISSIONS ===
try:
    from android.permissions import request_permissions, Permission, check_permission
    ANDROID_PERMISSIONS_AVAILABLE = True
    logger.info("Android permissions imported successfully")
except ImportError:
    ANDROID_PERMISSIONS_AVAILABLE = False
    logger.warning("Android permissions not available")
    request_permissions = lambda perms, callback=None: None
    class Permission:
        READ_EXTERNAL_STORAGE = "android.permission.READ_EXTERNAL_STORAGE"
        WRITE_EXTERNAL_STORAGE = "android.permission.WRITE_EXTERNAL_STORAGE"
        INTERNET = "android.permission.INTERNET"
    def check_permission(perm): return True

# === MUTAGEN IMPORTS ===
try:
    from mutagen import File
    from mutagen.mp3 import MP3
    MUTAGEN_AVAILABLE = True
    logger.info("Mutagen imported successfully")
except ImportError as e:
    MUTAGEN_AVAILABLE = False
    logger.warning(f"Mutagen not available: {e}")
    # Create fallback
    class File:
        def __init__(self, *args, **kwargs): pass
    class MP3:
        def __init__(self, *args, **kwargs): pass

# === SIMPLE TEST WIDGET ===
class SimpleTestWidget(MDBoxLayout):
    """واجهة بسيطة للاختبار"""
    
    def __init__(self, **kwargs):
        super(SimpleTestWidget, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # تسمية ترحيب
        welcome_label = MDLabel(
            text='مرحباً بك في مشغل الموسيقى العربي\nArabic Music Player Test',
            font_style='H5',
            halign='center',
            theme_text_color='Primary'
        )
        self.add_widget(welcome_label)
        
        # تسمية حالة
        self.status_label = MDLabel(
            text='التطبيق يعمل بنجاح!\nApp is working successfully!',
            font_style='Body1',
            halign='center',
            theme_text_color='Secondary'
        )
        self.add_widget(self.status_label)
        
        # زر اختبار
        test_button = MDRaisedButton(
            text='اختبار / Test',
            size_hint=(1, None),
            height='50dp'
        )
        test_button.bind(on_press=self.on_test_button)
        self.add_widget(test_button)
        
        # جدولة تحديث الحالة
        Clock.schedule_interval(self.update_status, 3.0)
        
    def on_test_button(self, instance):
        """معالج زر الاختبار"""
        self.status_label.text = 'تم الضغط على الزر!\nButton pressed!'
        logger.info("Test button pressed")
        
        # إعادة تعيين النص بعد ثانيتين
        Clock.schedule_once(self.reset_status, 2.0)
        
    def reset_status(self, dt):
        """إعادة تعيين نص الحالة"""
        self.status_label.text = 'التطبيق يعمل بنجاح!\nApp is working successfully!'
        
    def update_status(self, dt):
        """تحديث حالة التطبيق"""
        import time
        current_time = time.strftime('%H:%M:%S')
        self.status_label.text = f'التطبيق يعمل بنجاح!\nApp working! Time: {current_time}'

# === MAIN APP CLASS ===
class SimpleTestApp(MDApp):
    """تطبيق بسيط للاختبار"""
    
    def build(self):
        logger.info("Building Simple Test App")
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
        
        # طلب الأذونات
        self.request_permissions_safely()
        
        # إعداد النافذة
        try:
            Window.fullscreen = 'auto'
        except Exception as e:
            logger.warning(f"Failed to set window fullscreen: {e}")
        
        return SimpleTestWidget()
    
    def request_permissions_safely(self):
        """طلب الأذونات بشكل آمن"""
        if not ANDROID_PERMISSIONS_AVAILABLE:
            logger.info("Android permissions not available, skipping permission request")
            return

        try:
            required_permissions = [
                Permission.READ_EXTERNAL_STORAGE,
                Permission.WRITE_EXTERNAL_STORAGE,
                Permission.INTERNET
            ]

            missing_permissions = []
            for perm in required_permissions:
                try:
                    if not check_permission(perm):
                        missing_permissions.append(perm)
                except Exception as e:
                    logger.warning(f"Error checking permission {perm}: {e}")

            if missing_permissions:
                logger.info(f"Requesting missing permissions: {missing_permissions}")
                
                def permission_callback(permissions, results):
                    try:
                        for perm, result in zip(permissions, results):
                            if result:
                                logger.info(f"✅ Permission granted: {perm}")
                            else:
                                logger.warning(f"❌ Permission denied: {perm}")
                    except Exception as e:
                        logger.error(f"Error in permission callback: {e}")

                request_permissions(missing_permissions, permission_callback)
            else:
                logger.info("✅ All required permissions already granted")

        except Exception as e:
            logger.error(f"❌ Error requesting permissions: {e}")

    def on_stop(self):
        """تنظيف عند إغلاق التطبيق"""
        try:
            import gc
            gc.collect()
            logger.info("App stopped and memory cleaned")
        except Exception as e:
            logger.error(f"Error during app stop: {e}")

def main():
    """الدالة الرئيسية"""
    logger.info("Starting Arabic Music Player Test App")
    
    try:
        app = SimpleTestApp()
        app.run()
    except Exception as e:
        logger.error(f"Error running app: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == '__main__':
    main()
