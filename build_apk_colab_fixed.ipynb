{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github"}, "source": ["# بناء تطبيق مشغل الموسيقى العربي - النسخة المحسنة للأندرويد فقط\n", "\n", "هذا الملف المحدث يحتوي على نسخة محسنة مخصصة للأندرويد فقط مع أداء أفضل وحجم أصغر."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_requirements", "cellView": "form"}, "outputs": [], "source": ["# @title تثبيت المتطلبات الأساسية وإعداد البيئة { display-mode: \"form\" }\n", "\n", "# تثبيت المتطلبات الأساسية\n", "!pip install buildozer cython==0.29.33 pillow\n", "\n", "# تثبيت متطلبات النظام\n", "!apt-get update\n", "!apt-get install -y python3-pip build-essential git python3 python3-dev ffmpeg libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev libsdl2-ttf-dev libportmidi-dev libswscale-dev libavformat-dev libavcodec-dev zlib1g-dev\n", "\n", "# تثبيت متطلبات Android\n", "!apt-get install -y openjdk-17-jdk\n", "!apt-get install -y autoconf automake libtool\n", "\n", "# تثبيت أدوات إضافية\n", "!apt-get install -y libltdl-dev libffi-dev ccache\n", "\n", "# إعداد متغيرات البيئة\n", "import os\n", "os.environ['JAVA_HOME'] = '/usr/lib/jvm/java-17-openjdk-amd64'\n", "\n", "print(\"تم تثبيت جميع المتطلبات بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive", "cellView": "form"}, "outputs": [], "source": ["# @title ربط Google Drive { display-mode: \"form\" }\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "print(\"تم ربط Google Drive بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "extract_project", "cellView": "form"}, "outputs": [], "source": ["# @title استخراج ملفات المشروع وتنظيفها { display-mode: \"form\" }\n", "import os\n", "import zipfile\n", "import shutil\n", "\n", "# مسار ملف ZIP في Google Drive - قم بتغيير هذا المسار\n", "zip_path = \"/content/drive/MyDrive/ArabicPlayer_Android_Only.zip\"  # النسخة المحسنة للأندرويد\n", "\n", "# إنشاء مجلد للمشروع\n", "!mkdir -p /content/app\n", "\n", "# استخراج الملفات\n", "with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "    zip_ref.extractall('/content/app')\n", "\n", "# الانتقال إلى مجلد المشروع\n", "%cd /content/app\n", "\n", "# تنظيف الملفات غير الضرورية\n", "!rm -rf __pycache__ downloads music temp_covers converted_audio album_art\n", "!rm -f *.txt *.zip *.bak cover_*.jpg project.zip\n", "!find . -name \"*.pyc\" -delete\n", "\n", "print(\"تم استخراج وتنظيف ملفات المشروع بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_buildozer_spec", "cellView": "form"}, "outputs": [], "source": ["# @title إنشاء ملف buildozer.spec المحدث { display-mode: \"form\" }\n", "\n", "buildozer_spec = '''[app]\n", "title = ArabicPlayer\n", "package.name = arabicplayer\n", "package.domain = org.arabicplayer\n", "source.dir = .\n", "source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json\n", "source.include_patterns = fonts/*.ttf, images/*.png, images/*.jpg, default_covers/*.jpg, *.py, *.kv, *.json\n", "source.exclude_dirs = __pycache__, bin, .buildozer, venv, downloads, temp_covers, converted_audio, album_art, music\n", "source.exclude_patterns = *.pyc, *.pyo, *.pyd, *.so, *.dll, *.egg-info, *.zip, *.part, *.bak, *.txt\n", "version = 1.0\n", "entrypoint = main.py\n", "icon.filename = images/default_album_cover.png\n", "presplash.filename = images/default_album_cover.png\n", "orientation = portrait\n", "fullscreen = 1\n", "copy_libs = 1\n", "\n", "requirements = python3,kivy==2.3.0,kivymd==1.1.1,mutagen,pyjnius,android,python-bidi,arabic-reshaper,pillow,ffpyplayer,requests,plyer\n", "\n", "build_dir = ./bld\n", "\n", "[android]\n", "android.api = 33\n", "android.minapi = 21\n", "android.ndk = 25b\n", "android.ndk_api = 21\n", "android.sdk = 33\n", "android.skip_update = True\n", "android.archs = armeabi-v7a,arm64-v8a\n", "\n", "android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,<PERSON><PERSON>GE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS,RECORD_AUDIO\n", "android.allow_backup = 1\n", "android.private_storage = true\n", "android.accept_sdk_license = True\n", "android.logcat_filters = *:S python:D\n", "android.entrypoint = main.py\n", "android.numeric_version = 1\n", "android.use_ccache = 1\n", "android.wakelock = True\n", "\n", "android.gradle_repositories = https://jitpack.io\n", "android.gradle_dependencies = com.google.android.material:material:1.4.0, androidx.core:core:1.6.0, androidx.appcompat:appcompat:1.3.1\n", "android.add_jars = libs/*.jar\n", "android.add_aars = libs/*.aar\n", "\n", "android.enable_androidx = True\n", "android.enable_asset_packing = True\n", "android.enable_manifest_placeholders = True\n", "android.enable_gradle_daemon = True\n", "\n", "android.extra_manifest_xml = <meta-data android:name=\"android.max_aspect\" android:value=\"2.1\" />\n", "\n", "[buildozer]\n", "log_level = 2\n", "warn_on_root = 1\n", "\n", "# Android-only optimization settings\n", "android.optimize_python = 1\n", "android.strip_debug = 1\n", "android.enable_proguard = 0\n", "android.add_compile_options = -O2\n", "android.add_link_options = -s\n", "'''\n", "\n", "# كتابة ملف buildozer.spec\n", "with open('buildozer.spec', 'w') as f:\n", "    f.write(buildozer_spec)\n", "\n", "print(\"تم إنشاء ملف buildozer.spec المحدث بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_buildozerignore", "cellView": "form"}, "outputs": [], "source": ["# @title إنشاء ملف .buildozerignore { display-mode: \"form\" }\n", "\n", "buildozerignore_content = '''# Buildozer ignore file\n", "# Files and directories to exclude from APK build\n", "\n", "# Cache directories\n", "__pycache__/\n", "*.pyc\n", "*.pyo\n", "*.pyd\n", "\n", "# Development files\n", "*.txt\n", "*.zip\n", "*.bak\n", "*.part\n", "\n", "# Media directories with long filenames\n", "downloads/\n", "music/\n", "album_art/\n", "temp_covers/\n", "converted_audio/\n", "\n", "# Cover files in root directory\n", "cover_*.jpg\n", "\n", "# Build directories\n", ".buildozer/\n", "bin/\n", "bld/\n", "\n", "# Version control\n", ".git/\n", ".giti<PERSON>re\n", "\n", "# IDE files\n", ".vscode/\n", ".idea/\n", "*.swp\n", "*.swo\n", "\n", "# OS files\n", ".DS_Store\n", "Thumbs.db\n", "\n", "# Temporary files\n", "temp_*\n", "*.tmp\n", "'''\n", "\n", "# كتابة ملف .buildozerignore\n", "with open('.buildozerignore', 'w') as f:\n", "    f.write(buildozerignore_content)\n", "\n", "print(\"تم إنشاء ملف .buildozerignore بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_libs_dir", "cellView": "form"}, "outputs": [], "source": ["# @title إنشاء مجلد libs وتحضير البناء { display-mode: \"form\" }\n", "!mkdir -p libs\n", "\n", "# التحقق من الملفات المطلوبة\n", "!ls -la\n", "\n", "print(\"تم إنشاء مجلد libs والتحقق من الملفات بنجاح!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "build_apk", "cellView": "form"}, "outputs": [], "source": ["# @title بناء ملف APK { display-mode: \"form\" }\n", "# منع انقطاع الاتصال\n", "from IPython.display import display, Javascript\n", "display(Javascript('''\n", "function ClickConnect(){\n", "    console.log(\"Working\"); \n", "    document.querySelector(\"colab-toolbar-button#connect\").click() \n", "}\n", "setInterval(ClickConnect, 60000)\n", "'''))\n", "\n", "# تنظيف البناء السابق\n", "!buildozer android clean\n", "\n", "# بناء ملف APK مع حفظ السجلات\n", "!buildozer -v android debug > build_log.txt 2>&1\n", "\n", "# عرض نتيجة البناء\n", "!cat build_log.txt | tail -n 50\n", "\n", "print(\"\\nتم الانتهاء من عملية البناء!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "copy_apk", "cellView": "form"}, "outputs": [], "source": ["# @title نسخ ملف APK إلى Google Drive { display-mode: \"form\" }\n", "import os\n", "import shutil\n", "\n", "# التحقق من وجود ملف APK\n", "apk_path = '/content/app/bin/arabicplayer-1.0-debug.apk'\n", "if not os.path.exists(apk_path):\n", "    apk_path = '/content/app/bin/ArabicPlayer-1.0-debug.apk'\n", "\n", "if os.path.exists(apk_path):\n", "    # إنشاء مجلد في Google Drive\n", "    output_dir = '/content/drive/MyDrive/ArabicPlayerAPK'\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # نسخ ملف APK\n", "    shutil.copy(apk_path, os.path.join(output_dir, 'ArabicPlayer.apk'))\n", "    \n", "    # نسخ سجلات البناء\n", "    if os.path.exists('build_log.txt'):\n", "        shutil.copy('build_log.txt', os.path.join(output_dir, 'build_log.txt'))\n", "    \n", "    print(f\"✅ تم نسخ ملف APK بنجاح إلى: {output_dir}/ArabicPlayer.apk\")\n", "    print(f\"📱 يمكنك الآن تحميل الملف من Google Drive وتثبيته على هاتفك الأندرويد\")\n", "else:\n", "    print(\"❌ لم يتم العثور على ملف APK! تحقق من عملية البناء.\")\n", "    \n", "    # نسخ سجلات البناء للتحقق من الأخطاء\n", "    if os.path.exists('build_log.txt'):\n", "        output_dir = '/content/drive/MyDrive/ArabicPlayerAPK'\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        shutil.copy('build_log.txt', os.path.join(output_dir, 'build_log.txt'))\n", "        print(f\"📋 تم نسخ سجلات البناء إلى: {output_dir}/build_log.txt\")\n", "        \n", "    # التحقق من سجلات Buildozer\n", "    buildozer_log = '.buildozer/logs/buildozer.log'\n", "    if os.path.exists(buildozer_log):\n", "        output_dir = '/content/drive/MyDrive/ArabicPlayerAPK'\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        shutil.copy(buildozer_log, os.path.join(output_dir, 'buildozer.log'))\n", "        print(f\"📋 تم نسخ سجلات Buildozer إلى: {output_dir}/buildozer.log\")"]}], "metadata": {"colab": {"name": "build_arabic_player_apk_fixed.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}