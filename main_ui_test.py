"""
نسخة مبسطة لاختبار واجهة مشغل الموسيقى العربي
Simple version to test Arabic Music Player UI
"""

import os
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === SAFE AUDIO SETUP ===
def setup_audio_safely():
    """إعداد الصوت بشكل آمن للأندرويد"""
    try:
        os.environ['KIVY_AUDIO'] = 'pygame'
        os.environ['SDL_AUDIODRIVER'] = 'android'
        logger.info("✅ Audio provider set to pygame for Android")
        return True
    except Exception as e:
        logger.warning(f"Failed to set pygame audio: {e}")
        return False

# تشغيل إعداد الصوت
audio_setup_success = setup_audio_safely()

# إعداد النص العربي
os.environ['KIVY_TEXT'] = 'pil'

from kivy.config import Config
from kivy.utils import platform

# Set audio provider in the 'kivy' section
if not Config.has_section('kivy'):
    Config.add_section('kivy')

if audio_setup_success:
    try:
        Config.set('kivy', 'audio', 'pygame')
        logger.info("✅ Kivy audio provider set to: pygame for Android")
    except Exception as e:
        logger.warning(f"Failed to set audio provider 'pygame' in config: {e}")

# === KIVY IMPORTS ===
from kivy.uix.boxlayout import BoxLayout
from kivy.properties import ListProperty, NumericProperty, BooleanProperty, StringProperty
from kivy.clock import Clock
from kivy.core.window import Window
from kivy.lang import Builder

# === KIVYMD IMPORTS ===
try:
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.label import MDLabel
    from kivymd.uix.button import MDRaisedButton
    from kivymd.uix.snackbar import Snackbar
    from kivymd.uix.list import OneLineListItem
    KIVYMD_AVAILABLE = True
    logger.info("KivyMD imported successfully")
except ImportError as e:
    KIVYMD_AVAILABLE = False
    logger.error(f"KivyMD not available: {e}")
    raise ImportError("KivyMD is required for this application")

# === ANDROID PERMISSIONS ===
try:
    from android.permissions import request_permissions, Permission, check_permission
    ANDROID_PERMISSIONS_AVAILABLE = True
    logger.info("Android permissions imported successfully")
except ImportError:
    ANDROID_PERMISSIONS_AVAILABLE = False
    logger.warning("Android permissions not available")
    request_permissions = lambda perms, callback=None: None
    class Permission:
        READ_EXTERNAL_STORAGE = "android.permission.READ_EXTERNAL_STORAGE"
        WRITE_EXTERNAL_STORAGE = "android.permission.WRITE_EXTERNAL_STORAGE"
        INTERNET = "android.permission.INTERNET"
    def check_permission(perm): return True

# === FONT REGISTRATION ===
def register_system_font():
    """تسجيل الخط العربي بشكل آمن"""
    try:
        from kivy.core.text import LabelBase
        font_file = 'NotoNaskhArabic-VariableFont_wght.ttf'
        font_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts', font_file)

        if os.path.exists(font_path):
            logger.info(f"Found font file at: {font_path}")
            font_name = 'NotoNaskhArabic-VariableFont_wght'

            # Register the Arabic font
            LabelBase.register(
                name=font_name,
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            # Register as default font
            LabelBase.register(
                name='default',
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            logger.info(f"✅ Successfully registered {font_name} as the default font")
            return font_name
        else:
            logger.warning(f"❌ Font file not found at: {font_path}")
            return 'Roboto'
    except Exception as e:
        logger.error(f"❌ Error registering system font: {e}")
        return 'Roboto'

# Call the function to register system font at startup
system_font = register_system_font()

# === SIMPLE MUSIC PLAYER WIDGET ===
class MusicPlayer(BoxLayout):
    """مشغل موسيقى مبسط لاختبار الواجهة"""
    
    playlist = ListProperty()
    current_index = NumericProperty(-1)
    is_playing = BooleanProperty(False)
    
    def __init__(self, **kwargs):
        super(MusicPlayer, self).__init__(**kwargs)
        self.orientation = 'vertical'
        
        # إضافة بعض الأغاني التجريبية
        self.playlist = [
            "أغنية تجريبية 1 - Test Song 1",
            "أغنية تجريبية 2 - Test Song 2", 
            "أغنية تجريبية 3 - Test Song 3"
        ]
        
        logger.info("✅ MusicPlayer widget initialized")
        
    def show_message(self, message):
        """عرض رسالة للمستخدم"""
        try:
            snackbar = Snackbar(
                text=message,
                snackbar_x="10dp",
                snackbar_y="10dp",
                size_hint_x=(Window.width - 20) / Window.width
            )
            snackbar.open()
            logger.info(f"Showed message: {message}")
        except Exception as e:
            logger.error(f"Error showing message: {e}")
            print(f"Message: {message}")

# === MAIN APP CLASS ===
class UITestApp(MDApp):
    """تطبيق اختبار الواجهة"""
    
    def build(self):
        logger.info("Building UI Test App")
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
        
        # طلب الأذونات
        self.request_permissions_safely()
        
        # إعداد النافذة
        try:
            Window.fullscreen = 'auto'
        except Exception as e:
            logger.warning(f"Failed to set window fullscreen: {e}")
        
        # تحميل ملف KV المبسط
        try:
            if os.path.exists('MusicPlayer_simple_test.kv'):
                Builder.load_file('MusicPlayer_simple_test.kv')
                logger.info("✅ Loaded simple test KV file")
            else:
                logger.warning("❌ Simple test KV file not found, using default layout")
        except Exception as e:
            logger.error(f"❌ Error loading KV file: {e}")
        
        return MusicPlayer()
    
    def request_permissions_safely(self):
        """طلب الأذونات بشكل آمن"""
        if not ANDROID_PERMISSIONS_AVAILABLE:
            logger.info("Android permissions not available, skipping permission request")
            return

        try:
            required_permissions = [
                Permission.READ_EXTERNAL_STORAGE,
                Permission.WRITE_EXTERNAL_STORAGE,
                Permission.INTERNET
            ]

            missing_permissions = []
            for perm in required_permissions:
                try:
                    if not check_permission(perm):
                        missing_permissions.append(perm)
                except Exception as e:
                    logger.warning(f"Error checking permission {perm}: {e}")

            if missing_permissions:
                logger.info(f"Requesting missing permissions: {missing_permissions}")
                
                def permission_callback(permissions, results):
                    try:
                        for perm, result in zip(permissions, results):
                            if result:
                                logger.info(f"✅ Permission granted: {perm}")
                            else:
                                logger.warning(f"❌ Permission denied: {perm}")
                    except Exception as e:
                        logger.error(f"Error in permission callback: {e}")

                request_permissions(missing_permissions, permission_callback)
            else:
                logger.info("✅ All required permissions already granted")

        except Exception as e:
            logger.error(f"❌ Error requesting permissions: {e}")

    def on_stop(self):
        """تنظيف عند إغلاق التطبيق"""
        try:
            import gc
            gc.collect()
            logger.info("App stopped and memory cleaned")
        except Exception as e:
            logger.error(f"Error during app stop: {e}")

def main():
    """الدالة الرئيسية"""
    logger.info("Starting Arabic Music Player UI Test")
    
    try:
        app = UITestApp()
        app.run()
    except Exception as e:
        logger.error(f"Error running app: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == '__main__':
    main()
