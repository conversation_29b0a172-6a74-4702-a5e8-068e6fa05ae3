#!/usr/bin/env python3
"""
تشخيص أسباب تحطم التطبيق
App crash diagnosis tool
"""

import os
import logging
import subprocess
import json
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_crash_logs():
    """تحليل سجلات التحطم"""
    logger.info("🔍 Analyzing crash logs...")
    
    crash_indicators = [
        "FATAL EXCEPTION",
        "AndroidRuntime",
        "Process: org.arabicplayer.arabicplayer",
        "python crashed",
        "ImportError",
        "ModuleNotFoundError",
        "AttributeError",
        "NameError",
        "SyntaxError",
        "IndentationError",
        "KeyError",
        "ValueError",
        "TypeError",
        "FileNotFoundError",
        "PermissionError"
    ]
    
    try:
        # قراءة سجلات logcat
        result = subprocess.run(
            ['adb', 'logcat', '-d', '-v', 'time'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            logcat_output = result.stdout
            
            # البحث عن مؤشرات التحطم
            crash_lines = []
            for line in logcat_output.split('\n'):
                for indicator in crash_indicators:
                    if indicator.lower() in line.lower():
                        crash_lines.append(line.strip())
                        break
            
            if crash_lines:
                logger.info(f"🚨 Found {len(crash_lines)} potential crash indicators:")
                for line in crash_lines[-10:]:  # آخر 10 أسطر
                    logger.info(f"  📋 {line}")
                return crash_lines
            else:
                logger.info("✅ No obvious crash indicators found in logcat")
                return []
        else:
            logger.error("❌ Failed to read logcat")
            return []
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Logcat command timed out")
        return []
    except FileNotFoundError:
        logger.error("❌ ADB not found - make sure Android SDK is installed")
        return []
    except Exception as e:
        logger.error(f"❌ Error reading logcat: {e}")
        return []

def check_common_crash_causes():
    """فحص الأسباب الشائعة للتحطم"""
    logger.info("🔍 Checking common crash causes...")
    
    issues = []
    
    # 1. فحص الملفات الأساسية
    essential_files = ['main.py', 'MusicPlayer.kv']
    for file in essential_files:
        if not os.path.exists(file):
            issues.append(f"❌ Missing essential file: {file}")
        else:
            logger.info(f"✅ Found: {file}")
    
    # 2. فحص بناء الجملة في main.py
    if os.path.exists('main.py'):
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # محاولة تجميع الكود
            compile(content, 'main.py', 'exec')
            logger.info("✅ main.py syntax is valid")
        except SyntaxError as e:
            issues.append(f"❌ Syntax error in main.py: {e}")
        except Exception as e:
            issues.append(f"❌ Error checking main.py: {e}")
    
    # 3. فحص ملف KV
    if os.path.exists('MusicPlayer.kv'):
        try:
            with open('MusicPlayer.kv', 'r', encoding='utf-8') as f:
                kv_content = f.read()
            
            # فحص أساسي لملف KV
            if '#:kivy' not in kv_content:
                issues.append("⚠️ MusicPlayer.kv missing #:kivy directive")
            else:
                logger.info("✅ MusicPlayer.kv has valid header")
        except Exception as e:
            issues.append(f"❌ Error reading MusicPlayer.kv: {e}")
    
    # 4. فحص المكتبات المطلوبة
    try:
        import kivy
        logger.info(f"✅ Kivy {kivy.__version__} available")
    except ImportError:
        issues.append("❌ Kivy not available")
    
    try:
        import kivymd
        logger.info(f"✅ KivyMD {kivymd.__version__} available")
    except ImportError:
        logger.info("ℹ️ KivyMD not available (optional)")
    
    # 5. فحص الأذونات في buildozer.spec
    if os.path.exists('buildozer.spec'):
        try:
            with open('buildozer.spec', 'r') as f:
                spec_content = f.read()
            
            if 'android.permissions' in spec_content:
                logger.info("✅ Permissions defined in buildozer.spec")
            else:
                issues.append("⚠️ No permissions defined in buildozer.spec")
        except Exception as e:
            issues.append(f"❌ Error reading buildozer.spec: {e}")
    
    return issues

def generate_crash_report():
    """إنشاء تقرير شامل عن التحطم"""
    logger.info("📋 Generating crash report...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "crash_analysis": {},
        "common_issues": [],
        "recommendations": []
    }
    
    # تحليل سجلات التحطم
    crash_logs = analyze_crash_logs()
    report["crash_analysis"]["logcat_crashes"] = crash_logs
    
    # فحص الأسباب الشائعة
    common_issues = check_common_crash_causes()
    report["common_issues"] = common_issues
    
    # توصيات الإصلاح
    recommendations = []
    
    if crash_logs:
        if any("ImportError" in log for log in crash_logs):
            recommendations.append("🔧 Use minimal buildozer.spec with fewer libraries")
        if any("PermissionError" in log for log in crash_logs):
            recommendations.append("🔧 Check Android permissions in buildozer.spec")
        if any("AttributeError" in log for log in crash_logs):
            recommendations.append("🔧 Check for missing attributes in main.py")
    
    if common_issues:
        recommendations.append("🔧 Fix the issues listed in common_issues section")
    
    if not crash_logs and not common_issues:
        recommendations.append("✅ No obvious issues found - try crash-resistant version")
    
    recommendations.extend([
        "🔧 Use main_crash_fix.py instead of main.py",
        "🔧 Use buildozer_crash_fix.spec for minimal build",
        "🔧 Test on different Android versions",
        "🔧 Check device logs immediately after crash"
    ])
    
    report["recommendations"] = recommendations
    
    return report

def create_crash_fix_package():
    """إنشاء حزمة إصلاح التحطم"""
    logger.info("📦 Creating crash fix package...")
    
    try:
        # نسخ الملفات المقاومة للتحطم
        files_to_copy = [
            ('main_crash_fix.py', 'main.py'),
            ('MusicPlayer_crash_fix.kv', 'MusicPlayer.kv'),
            ('buildozer_crash_fix.spec', 'buildozer.spec')
        ]
        
        backup_files = []
        
        for src, dst in files_to_copy:
            if os.path.exists(src):
                # إنشاء نسخة احتياطية
                if os.path.exists(dst):
                    backup_name = f"{dst}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    os.rename(dst, backup_name)
                    backup_files.append(backup_name)
                    logger.info(f"📁 Backed up {dst} to {backup_name}")
                
                # نسخ الملف الجديد
                import shutil
                shutil.copy(src, dst)
                logger.info(f"✅ Copied {src} to {dst}")
            else:
                logger.warning(f"⚠️ Source file not found: {src}")
        
        logger.info("✅ Crash fix package created successfully")
        logger.info("📋 Next steps:")
        logger.info("   1. buildozer android clean")
        logger.info("   2. buildozer android debug")
        logger.info("   3. Test the new APK")
        
        return True, backup_files
        
    except Exception as e:
        logger.error(f"❌ Failed to create crash fix package: {e}")
        return False, []

def main():
    """الدالة الرئيسية"""
    print("🔍 Arabic Music Player - Crash Diagnosis Tool")
    print("=" * 50)
    
    # إنشاء تقرير التحطم
    report = generate_crash_report()
    
    # عرض النتائج
    print("\n📋 CRASH ANALYSIS REPORT")
    print("=" * 30)
    
    if report["crash_analysis"]["logcat_crashes"]:
        print(f"\n🚨 Found {len(report['crash_analysis']['logcat_crashes'])} crash indicators in logcat:")
        for crash in report["crash_analysis"]["logcat_crashes"][-5:]:
            print(f"   📋 {crash}")
    else:
        print("\n✅ No crash indicators found in logcat")
    
    if report["common_issues"]:
        print(f"\n⚠️ Common issues found ({len(report['common_issues'])}):")
        for issue in report["common_issues"]:
            print(f"   {issue}")
    else:
        print("\n✅ No common issues detected")
    
    print(f"\n💡 Recommendations ({len(report['recommendations'])}):")
    for rec in report["recommendations"]:
        print(f"   {rec}")
    
    # حفظ التقرير
    report_file = f"crash_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n📄 Report saved to: {report_file}")
    except Exception as e:
        logger.error(f"Failed to save report: {e}")
    
    # اقتراح إنشاء حزمة الإصلاح
    print("\n🔧 CRASH FIX OPTIONS")
    print("=" * 25)
    print("1. Create crash-resistant version automatically")
    print("2. Manual fix (use provided files)")
    print("3. Exit")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            print("\n📦 Creating crash-resistant version...")
            success, backups = create_crash_fix_package()
            if success:
                print("✅ Crash-resistant version created!")
                print("🚀 You can now run: buildozer android debug")
            else:
                print("❌ Failed to create crash-resistant version")
        
        elif choice == "2":
            print("\n📋 Manual fix instructions:")
            print("   1. Copy main_crash_fix.py to main.py")
            print("   2. Copy MusicPlayer_crash_fix.kv to MusicPlayer.kv")
            print("   3. Copy buildozer_crash_fix.spec to buildozer.spec")
            print("   4. Run: buildozer android clean")
            print("   5. Run: buildozer android debug")
        
        else:
            print("👋 Exiting...")
    
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        logger.error(f"Error in user interaction: {e}")

if __name__ == "__main__":
    main()
