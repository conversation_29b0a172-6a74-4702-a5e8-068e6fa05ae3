# ملف KV بسيط للاختبار
# Simple KV file for testing

<SimpleTestWidget>:
    orientation: 'vertical'
    padding: dp(20)
    spacing: dp(15)
    
    MDLabel:
        text: 'مرحباً بك في مشغل الموسيقى العربي\\nArabic Music Player Test'
        font_style: 'H5'
        halign: 'center'
        theme_text_color: 'Primary'
        size_hint_y: None
        height: dp(100)
        
    MDLabel:
        id: status_label
        text: 'التطبيق يعمل بنجاح!\\nApp is working successfully!'
        font_style: 'Body1'
        halign: 'center'
        theme_text_color: 'Secondary'
        size_hint_y: None
        height: dp(80)
        
    MDRaisedButton:
        text: 'اختبار / Test'
        size_hint: 1, None
        height: dp(50)
        on_press: root.on_test_button(self)
        
    Widget:
        # مساحة فارغة
        size_hint_y: 1
