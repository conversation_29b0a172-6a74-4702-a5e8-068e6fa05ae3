"""
Arabic text handling utilities for the Music Player application.
This module provides functions to properly display Arabic text in the application.
"""

import logging
import re

# Configure logging
logger = logging.getLogger(__name__)

# Try to import the required libraries
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    logger.info("Arabic text support enabled (python-bidi and arabic-reshaper loaded)")
except ImportError as e:
    ARABIC_SUPPORT = False
    logger.warning(f"Arabic text support disabled: {e}")
    logger.warning("Install python-bidi and arabic-reshaper for proper Arabic text display")

def contains_arabic(text):
    """
    Check if the text contains Arabic characters.
    
    Args:
        text (str): The text to check
        
    Returns:
        bool: True if the text contains Arabic characters, False otherwise
    """
    if not text or not isinstance(text, str):
        return False
    
    # Arabic Unicode range (U+0600 to U+06FF)
    arabic_pattern = re.compile(r'[\u0600-\u06FF]')
    return bool(arabic_pattern.search(text))

def reshape_arabic_text(text):
    """
    Reshape Arabic text for proper display.
    
    Args:
        text (str): The text to reshape
        
    Returns:
        str: The reshaped text
    """
    if not text or not isinstance(text, str):
        return text
    
    # If Arabic support is not available, return the original text
    if not ARABIC_SUPPORT:
        return text
    
    # Only process text that contains Arabic characters
    if not contains_arabic(text):
        return text
    
    try:
        # Reshape the Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        # Apply bidirectional algorithm
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        logger.error(f"Error reshaping Arabic text: {e}")
        return text

def get_display_text(text, always_process=False):
    """
    Get the properly formatted text for display.
    
    Args:
        text (str): The text to format
        always_process (bool): Whether to always process the text, even if it doesn't contain Arabic
        
    Returns:
        str: The formatted text
    """
    if not text or not isinstance(text, str):
        return text
    
    # If Arabic support is not available, return the original text
    if not ARABIC_SUPPORT:
        return text
    
    # Process the text if it contains Arabic or if always_process is True
    if always_process or contains_arabic(text):
        return reshape_arabic_text(text)
    
    return text
