#!/usr/bin/env python3
"""
سكريبت سريع لإصلاح مشاكل Gradle وإنشاء APK
Quick script to fix Gradle issues and create APK
"""

import os
import shutil
import subprocess
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        logger.info(f"🔄 {description}: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout[:200]}...")
            return True
        else:
            logger.error(f"❌ {description} failed")
            if result.stderr:
                logger.error(f"Error: {result.stderr[:500]}...")
            return False
    except Exception as e:
        logger.error(f"❌ Exception in {description}: {e}")
        return False

def clean_buildozer():
    """تنظيف بيئة buildozer"""
    logger.info("🧹 Cleaning buildozer environment...")
    
    # حذف مجلد .buildozer
    if os.path.exists('.buildozer'):
        try:
            shutil.rmtree('.buildozer')
            logger.info("✅ Removed .buildozer directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove .buildozer: {e}")
    
    # حذف مجلد bin
    if os.path.exists('bin'):
        try:
            shutil.rmtree('bin')
            logger.info("✅ Removed bin directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove bin: {e}")
    
    # تشغيل buildozer clean
    run_command("buildozer android clean", "Buildozer clean")

def setup_simple_config():
    """إعداد تكوين مبسط"""
    logger.info("⚙️ Setting up simplified configuration...")
    
    # نسخ الإعدادات المبسطة
    if os.path.exists('buildozer_gradle_fix.spec'):
        try:
            shutil.copy('buildozer_gradle_fix.spec', 'buildozer.spec')
            logger.info("✅ Copied simplified buildozer.spec")
        except Exception as e:
            logger.error(f"❌ Failed to copy buildozer.spec: {e}")
    
    # نسخ main.py المبسط للاختبار
    if os.path.exists('main_gradle_test.py'):
        try:
            # نسخ احتياطية من main.py الأصلي
            if os.path.exists('main.py'):
                shutil.copy('main.py', 'main_original_backup.py')
                logger.info("✅ Created backup of original main.py")
            
            shutil.copy('main_gradle_test.py', 'main.py')
            logger.info("✅ Using simplified main.py for testing")
        except Exception as e:
            logger.error(f"❌ Failed to setup main.py: {e}")

def fix_gradle_permissions():
    """إصلاح أذونات Gradle"""
    logger.info("🔧 Fixing Gradle permissions...")
    
    # البحث عن ملفات gradlew وإصلاح الأذونات
    gradle_files = []
    for root, dirs, files in os.walk('.buildozer'):
        for file in files:
            if file == 'gradlew':
                gradle_files.append(os.path.join(root, file))
    
    for gradle_file in gradle_files:
        try:
            os.chmod(gradle_file, 0o755)
            logger.info(f"✅ Fixed permissions for {gradle_file}")
        except Exception as e:
            logger.error(f"❌ Failed to fix permissions for {gradle_file}: {e}")

def build_apk():
    """بناء APK"""
    logger.info("🏗️ Building APK...")
    
    # محاولة البناء
    success = run_command("buildozer android debug", "Building APK")
    
    if success:
        logger.info("🎉 APK built successfully!")
        
        # البحث عن ملف APK
        apk_files = []
        if os.path.exists('bin'):
            for file in os.listdir('bin'):
                if file.endswith('.apk'):
                    apk_files.append(os.path.join('bin', file))
        
        if apk_files:
            for apk in apk_files:
                size = os.path.getsize(apk) / (1024 * 1024)  # MB
                logger.info(f"📱 APK created: {apk} ({size:.2f} MB)")
        
        return True
    else:
        logger.error("❌ APK build failed")
        return False

def diagnose_gradle_error():
    """تشخيص أخطاء Gradle"""
    logger.info("🔍 Diagnosing Gradle errors...")
    
    # البحث عن ملفات السجل
    log_files = []
    for root, dirs, files in os.walk('.buildozer'):
        for file in files:
            if file.endswith('.log') or 'build' in file.lower():
                log_files.append(os.path.join(root, file))
    
    # فحص آخر ملفات السجل
    for log_file in log_files[-3:]:  # آخر 3 ملفات
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                if 'FAILED' in content or 'ERROR' in content:
                    logger.info(f"📄 Found errors in {log_file}")
                    # طباعة آخر 10 أسطر من الخطأ
                    lines = content.split('\n')
                    error_lines = [line for line in lines if 'FAILED' in line or 'ERROR' in line]
                    for line in error_lines[-5:]:
                        logger.error(f"   {line}")
        except Exception as e:
            logger.warning(f"Could not read {log_file}: {e}")

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 Starting Gradle fix and APK build process...")
    
    print("=" * 60)
    print("🎯 Arabic Music Player - Gradle Fix & Build")
    print("=" * 60)
    
    # الخطوة 1: تنظيف البيئة
    print("\n📋 Step 1: Cleaning environment...")
    clean_buildozer()
    
    # الخطوة 2: إعداد التكوين المبسط
    print("\n📋 Step 2: Setting up simplified configuration...")
    setup_simple_config()
    
    # الخطوة 3: محاولة البناء
    print("\n📋 Step 3: Building APK...")
    success = build_apk()
    
    if not success:
        # الخطوة 4: إصلاح أذونات Gradle ومحاولة مرة أخرى
        print("\n📋 Step 4: Fixing Gradle permissions and retrying...")
        fix_gradle_permissions()
        success = build_apk()
    
    if not success:
        # الخطوة 5: تشخيص الأخطاء
        print("\n📋 Step 5: Diagnosing errors...")
        diagnose_gradle_error()
        
        print("\n💡 Suggested next steps:")
        print("1. Check the error logs above")
        print("2. Try with even simpler requirements:")
        print("   requirements = python3,kivy,pyjnius,android")
        print("3. Try different API levels (28, 29, 30, 31)")
        print("4. Check GRADLE_TROUBLESHOOTING.md for detailed solutions")
    else:
        print("\n🎉 SUCCESS! APK created successfully!")
        print("📱 Check the 'bin' folder for your APK file")
        print("🚀 Ready for testing on Android device!")

if __name__ == "__main__":
    main()
