# قائمة شاملة بجميع المكتبات المطلوبة للتطبيق على الأندرويد

## 🎯 المكتبات الأساسية (Core Libraries)

### 1. Python والإطار الأساسي
```
python3==3.11.6          # إصدار Python المطلوب
kivy==2.3.0              # إطار العمل الأساسي للواجهة
kivymd==1.1.1            # مكونات Material Design
pyjnius                  # للتفاعل مع Java/Android APIs
android                  # دعم منصة الأندرويد
```

## 🎵 مكتبات معالجة الصوت والوسائط

### 2. تشغيل ومعالجة الصوت
```
mutagen==1.47.0          # قراءة metadata للملفات الصوتية
ffpyplayer               # تشغيل الصوت والفيديو
pygame                   # بديل لتشغيل الصوت
numpy                    # معالجة البيانات الصوتية (audio_enhancer.py)
```

**سبب الحاجة:**
- `mutagen`: لقراءة معلومات الأغاني (العنوان، الفنان، المدة)
- `ffpyplayer`: المشغل الأساسي للصوت في Kivy
- `pygame`: بديل أكثر استقرارًا لتشغيل الصوت على الأندرويد
- `numpy`: مطلوبة في audio_enhancer.py لمعالجة الإشارات الصوتية

## 🌐 مكتبات الشبكة والتحميل

### 3. الاتصال بالإنترنت والتحميل
```
requests==2.31.0         # طلبات HTTP
urllib3                  # مكتبة HTTP منخفضة المستوى
certifi                  # شهادات SSL
charset-normalizer       # تشفير النصوص
idna                     # معالجة أسماء النطاقات الدولية
```

**سبب الحاجة:**
- `requests`: للبحث في APIs مثل Deezer وتحميل الملفات
- `urllib3`: تبعية أساسية لـ requests
- `certifi`: للتحقق من شهادات SSL عند الاتصال بالمواقع
- `charset-normalizer` و `idna`: لمعالجة النصوص والروابط الدولية

### 4. تحميل من YouTube
```
pytube                   # تحميل من YouTube (الطريقة الأولى)
yt-dlp                   # بديل أكثر استقرارًا لـ pytube
```

**سبب الحاجة:**
- `pytube`: مكتبة بسيطة لتحميل فيديوهات YouTube
- `yt-dlp`: بديل أكثر استقرارًا ودعمًا لمواقع متعددة
- التطبيق يستخدم كلاهما كبدائل في حالة فشل أحدهما

## 🔤 مكتبات النص العربي

### 5. دعم اللغة العربية
```
python-bidi==0.4.2       # خوارزمية النص ثنائي الاتجاه
arabic-reshaper==3.0.0   # إعادة تشكيل النص العربي
```

**سبب الحاجة:**
- `python-bidi`: لعرض النص العربي بالاتجاه الصحيح (من اليمين لليسار)
- `arabic-reshaper`: لربط الأحرف العربية بشكل صحيح
- مطلوبة في arabic_utils.py لمعالجة النصوص العربية

## 🖼️ مكتبات معالجة الصور

### 6. الصور والرسومات
```
pillow==10.0.1           # معالجة وتحرير الصور
```

**سبب الحاجة:**
- `pillow`: لمعالجة صور أغلفة الألبومات
- إنشاء صور افتراضية عند عدم وجود غلاف
- تحسين وضغط الصور

## 🔧 مكتبات النظام والأداء

### 7. مراقبة النظام والأداء
```
plyer==2.1.0             # الوصول لميزات النظام
psutil                   # مراقبة الأداء والذاكرة
```

**سبب الحاجة:**
- `plyer`: للوصول لميزات الأندرويد مثل الإشعارات والملفات
- `psutil`: مطلوبة في performance_optimizer.py لمراقبة استخدام الذاكرة والمعالج

## 🛠️ مكتبات الدعم والأدوات

### 8. أدوات مساعدة
```
six                      # توافق Python 2/3
setuptools               # أدوات التثبيت
wheel                    # تنسيق توزيع Python
```

**سبب الحاجة:**
- `six`: للتوافق مع مكتبات قديمة
- `setuptools` و `wheel`: مطلوبة لتثبيت بعض المكتبات الأخرى

## 📱 الأذونات المطلوبة على الأندرويد

```ini
android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,MANAGE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS,RECORD_AUDIO,ACCESS_WIFI_STATE,CHANGE_WIFI_STATE,VIBRATE
```

**شرح الأذونات:**
- `READ_EXTERNAL_STORAGE`: قراءة ملفات الموسيقى
- `WRITE_EXTERNAL_STORAGE`: حفظ الملفات المحملة
- `INTERNET`: الاتصال بالإنترنت للبحث والتحميل
- `MANAGE_EXTERNAL_STORAGE`: إدارة الملفات (Android 11+)
- `FOREGROUND_SERVICE`: تشغيل الموسيقى في الخلفية
- `WAKE_LOCK`: منع النوم أثناء التشغيل
- `ACCESS_NETWORK_STATE`: فحص حالة الشبكة
- `MODIFY_AUDIO_SETTINGS`: تعديل إعدادات الصوت
- `RECORD_AUDIO`: للمؤثرات الصوتية
- `ACCESS_WIFI_STATE`: فحص حالة WiFi
- `CHANGE_WIFI_STATE`: تغيير إعدادات WiFi
- `VIBRATE`: الاهتزاز للتنبيهات

## ⚠️ مكتبات قد تسبب مشاكل

### مكتبات اختيارية (يمكن حذفها للاختبار):
```
pytube                   # قد تفشل أحيانًا
yt-dlp                   # بديل لـ pytube
psutil                   # قد لا تعمل على بعض أجهزة الأندرويد
numpy                    # كبيرة الحجم، قد تبطئ التطبيق
```

## 🎯 نصائح لتجنب التحطم

1. **ابدأ بالمكتبات الأساسية فقط**
2. **أضف المكتبات تدريجيًا**
3. **اختبر على جهاز أندرويد حقيقي**
4. **راقب logcat للأخطاء**
5. **استخدم try/except لجميع الاستيرادات**

## 📋 ملف buildozer.spec النهائي

```ini
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,mutagen==1.47.0,pyjnius,android,python-bidi==0.4.2,arabic-reshaper==3.0.0,pillow==10.0.1,requests==2.31.0,plyer==2.1.0,certifi,urllib3,charset-normalizer,idna,numpy,ffpyplayer,pygame,psutil,six,setuptools,wheel,pytube,yt-dlp
```
