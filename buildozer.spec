[app]
title = ArabicPlayer
package.name = arabicplayer
package.domain = org.arabicplayer
source.dir = .
source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json
source.include_patterns = fonts/*.ttf, images/*.png, images/*.jpg, default_covers/*.jpg, *.py, *.kv, *.json
source.exclude_dirs = __pycache__, bin, .buildozer, venv, downloads, temp_covers, converted_audio, album_art, music
source.exclude_patterns = *.pyc, *.pyo, *.pyd, *.so, *.dll, *.egg-info, *.zip, *.part, *.bak, *.txt
version = 1.0
entrypoint = main.py
icon.filename = images/default_album_cover.png
presplash.filename = images/default_album_cover.png
orientation = portrait
fullscreen = 1
copy_libs = 1

requirements = python3,kivy==2.3.0,kivymd==1.1.1,mutagen,pyjnius,android,python-bidi,arabic-reshaper,pillow,ffpyplayer,requests,plyer

build_dir = ./bld

[android]
android.api = 33
android.minapi = 21
android.ndk = 25b
android.ndk_api = 21
android.sdk = 33
android.skip_update = True
android.archs = armeabi-v7a,arm64-v8a

android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,MANAGE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS,RECORD_AUDIO
android.allow_backup = 1
android.private_storage = true
android.accept_sdk_license = True
android.logcat_filters = *:S python:D
android.entrypoint = main.py
android.numeric_version = 1
android.use_ccache = 1
android.wakelock = True

android.gradle_repositories = https://jitpack.io
android.gradle_dependencies = com.google.android.material:material:1.4.0, androidx.core:core:1.6.0, androidx.appcompat:appcompat:1.3.1
android.add_jars = libs/*.jar
android.add_aars = libs/*.aar

android.enable_androidx = True
android.enable_asset_packing = True
android.enable_manifest_placeholders = True
android.enable_gradle_daemon = True

android.extra_manifest_xml = <meta-data android:name="android.max_aspect" android:value="2.1" />

android.manifest.intent_filters =
    [
        {
            "action": ["android.intent.action.SEND"],
            "category": ["android.intent.category.DEFAULT"],
            "data_mimetypes": ["text/plain"],
            "meta_data": [
                {"name": "android.service.chooser.chooser_target_service", "value": ".ShareTargetService"}
            ],
            "alias": "Share URL to Music Player"
        },
        {
            "action": ["android.intent.action.VIEW"],
            "category": ["android.intent.category.DEFAULT", "android.intent.category.BROWSABLE"],
            "data_schemes": ["http", "https"],
            "data_path_prefixes": ["/"],
            "data_host_patterns": [".*youtube\\.com.*", ".*youtu\\.be.*", ".*soundcloud\\.com.*"]
        },
        {
            "action": ["android.intent.action.PROCESS_TEXT"],
            "category": ["android.intent.category.DEFAULT"],
            "data_mimetypes": ["text/plain"]
        }
    ]

[buildozer]
log_level = 2
warn_on_root = 1

# Android-only optimization settings
android.optimize_python = 1
android.strip_debug = 1
android.enable_proguard = 0
android.add_compile_options = -O2
android.add_link_options = -s
