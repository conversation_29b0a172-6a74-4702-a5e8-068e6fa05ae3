# === BEGIN AUDIO PROVIDER SELECTION (must be before any kivy imports) ===
import os
import time
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# تعيين مستوى السجل لمكتبة kivy لتجاهل الأخطاء غير الهامة
kivy_logger = logging.getLogger('kivy')
kivy_logger.setLevel(logging.CRITICAL)

# تعيين مستوى السجل لمكتبة ffpyplayer لتجاهل الأخطاء غير الهامة
ffpyplayer_logger = logging.getLogger('ffpyplayer')
ffpyplayer_logger.setLevel(logging.CRITICAL)

# إضافة مرشح مخصص لتجاهل أخطاء محددة
class IgnoreSpecificErrorsFilter(logging.Filter):
    def filter(self, record):
        if "Channel layout change is not supported" in record.getMessage():
            return False
        if "Not yet implemented in FFmpeg" in record.getMessage():
            return False
        return True

# إضافة المرشح إلى سجلات kivy و ffpyplayer
kivy_logger.addFilter(IgnoreSpecificErrorsFilter())
ffpyplayer_logger.addFilter(IgnoreSpecificErrorsFilter())

# === SAFE IMPORTS WITH ERROR HANDLING ===
# استيراد المكتبات المخصصة مع معالجة الأخطاء
try:
    from custom_slider import CustomSlider
    CUSTOM_SLIDER_AVAILABLE = True
    logger.info("CustomSlider imported successfully")
except ImportError as e:
    CUSTOM_SLIDER_AVAILABLE = False
    logger.warning(f"CustomSlider not available: {e}")

try:
    from playing_indicator import PlayingIndicator
    PLAYING_INDICATOR_AVAILABLE = True
    logger.info("PlayingIndicator imported successfully")
except ImportError as e:
    PLAYING_INDICATOR_AVAILABLE = False
    logger.warning(f"PlayingIndicator not available: {e}")

try:
    from color_circle import ColorCircle
    COLOR_CIRCLE_AVAILABLE = True
    logger.info("ColorCircle imported successfully")
except ImportError as e:
    COLOR_CIRCLE_AVAILABLE = False
    logger.warning(f"ColorCircle not available: {e}")

try:
    from audio_enhancer import AudioEnhancer
    AUDIO_ENHANCER_AVAILABLE = True
    logger.info("AudioEnhancer imported successfully")
except ImportError as e:
    AUDIO_ENHANCER_AVAILABLE = False
    logger.warning(f"AudioEnhancer not available: {e}")

try:
    from download_manager import DownloadManager
    DOWNLOAD_MANAGER_AVAILABLE = True
    logger.info("DownloadManager imported successfully")
except ImportError as e:
    DOWNLOAD_MANAGER_AVAILABLE = False
    logger.warning(f"DownloadManager not available: {e}")

try:
    from download_screen import DownloadScreen
    DOWNLOAD_SCREEN_AVAILABLE = True
    logger.info("DownloadScreen imported successfully")
except ImportError as e:
    DOWNLOAD_SCREEN_AVAILABLE = False
    logger.warning(f"DownloadScreen not available: {e}")

try:
    from search_screen import SearchScreen
    SEARCH_SCREEN_AVAILABLE = True
    logger.info("SearchScreen imported successfully")
except ImportError as e:
    SEARCH_SCREEN_AVAILABLE = False
    logger.warning(f"SearchScreen not available: {e}")

try:
    from performance_optimizer import PerformanceOptimizer
    PERFORMANCE_OPTIMIZER_AVAILABLE = True
    logger.info("PerformanceOptimizer imported successfully")
except ImportError as e:
    PERFORMANCE_OPTIMIZER_AVAILABLE = False
    logger.warning(f"PerformanceOptimizer not available: {e}")

# === SAFE AUDIO SETUP ===
def setup_audio_safely():
    """إعداد الصوت بشكل آمن للأندرويد"""
    try:
        # تجربة pygame أولاً (الأكثر استقراراً على الأندرويد)
        os.environ['KIVY_AUDIO'] = 'pygame'
        os.environ['SDL_AUDIODRIVER'] = 'android'
        os.environ['KIVY_AUDIO_BUFFER_SIZE'] = '4096'
        os.environ['SDL_AUDIO_FREQUENCY'] = '44100'
        logger.info("✅ Audio provider set to pygame for Android")
        return True
    except Exception as e:
        logger.warning(f"Failed to set pygame audio: {e}")
        try:
            # تجربة gstplayer كبديل
            os.environ['KIVY_AUDIO'] = 'gstplayer'
            logger.info("✅ Audio provider set to gstplayer")
            return True
        except Exception as e2:
            logger.error(f"Failed to set any audio provider: {e2}")
            return False

# تشغيل إعداد الصوت
audio_setup_success = setup_audio_safely()

# Add environment variable to improve Arabic text rendering
os.environ['KIVY_TEXT'] = 'pil'

from kivy.config import Config
from kivy.utils import platform

# Set audio provider in the 'kivy' section
if not Config.has_section('kivy'):
    Config.add_section('kivy')

# Set NotoNaskhArabic as the default font for the entire application
try:
    Config.set('kivy', 'default_font', ['NotoNaskhArabic-VariableFont_wght', 'fonts/NotoNaskhArabic-VariableFont_wght.ttf'])
    logger.info("Default font set to NotoNaskhArabic")
except Exception as e:
    logger.warning(f"Failed to set default font: {e}")

# تعيين مزود الصوت إلى pygame فقط للأندرويد
if audio_setup_success:
    try:
        Config.set('kivy', 'audio', 'pygame')
        logger.info("✅ Kivy audio provider set to: pygame for Android")
    except Exception as e:
        logger.warning(f"Failed to set audio provider 'pygame' in config: {e}")

# === KIVY IMPORTS ===
from kivy.metrics import dp, sp
from kivy.lang import Builder
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.screenmanager import ScreenManager, Screen, SlideTransition, FadeTransition, CardTransition, SwapTransition
from kivy.clock import Clock, mainthread
from kivy.core.audio import SoundLoader
from kivy.properties import ObjectProperty, NumericProperty, BooleanProperty, ListProperty, StringProperty
from kivy.uix.dropdown import DropDown
from kivy.uix.button import Button
from kivy.uix.image import Image, AsyncImage
from kivy.core.window import Window
from kivy.utils import platform
from kivy.animation import Animation
from kivy.factory import Factory
from kivy.graphics import Color, PushMatrix, PopMatrix, Rotate
from kivy.uix.progressbar import ProgressBar
from kivy.uix.behaviors import ButtonBehavior
from kivy.graphics import Color, Line, Ellipse
from kivy.uix.relativelayout import RelativeLayout
from kivy.core.image import Image as CoreImage
from kivy.core.text import LabelBase
from kivy.uix.widget import Widget

# === KIVYMD IMPORTS ===
try:
    from kivymd.uix.list import OneLineListItem
    from kivymd.uix.dialog import MDDialog
    from kivymd.uix.button import MDFlatButton
    from kivymd.app import MDApp
    from kivymd.uix.button import MDRaisedButton, MDIconButton
    from kivymd.uix.card import MDCard
    from kivymd.uix.label import MDLabel
    from kivymd.uix.list import OneLineAvatarIconListItem, ILeftBodyTouch, IRightBodyTouch
    from kivymd.uix.slider import MDSlider
    from kivymd.uix.toolbar import MDTopAppBar
    from kivymd.uix.toolbar.toolbar import ActionTopAppBarButton
    from kivymd.uix.navigationdrawer import MDNavigationLayout, MDNavigationDrawer
    from kivymd.uix.filemanager import MDFileManager
    from kivymd.uix.menu import MDDropdownMenu
    from kivymd.uix.snackbar import Snackbar
    KIVYMD_AVAILABLE = True
    logger.info("KivyMD imported successfully")
except ImportError as e:
    KIVYMD_AVAILABLE = False
    logger.error(f"KivyMD not available: {e}")
    # يجب إيقاف التطبيق إذا لم تكن KivyMD متاحة
    raise ImportError("KivyMD is required for this application")

# === ANDROID PERMISSIONS ===
try:
    from android.permissions import request_permissions, Permission, check_permission
    ANDROID_PERMISSIONS_AVAILABLE = True
    logger.info("Android permissions imported successfully")
except ImportError:
    ANDROID_PERMISSIONS_AVAILABLE = False
    logger.warning("Android permissions not available")
    # Fallback if android.permissions is not available during build
    request_permissions = lambda perms, callback=None: None
    class Permission:
        READ_EXTERNAL_STORAGE = "android.permission.READ_EXTERNAL_STORAGE"
        WRITE_EXTERNAL_STORAGE = "android.permission.WRITE_EXTERNAL_STORAGE"
        FOREGROUND_SERVICE = "android.permission.FOREGROUND_SERVICE"
        INTERNET = "android.permission.INTERNET"
        MANAGE_EXTERNAL_STORAGE = "android.permission.MANAGE_EXTERNAL_STORAGE"
        WAKE_LOCK = "android.permission.WAKE_LOCK"
    def check_permission(perm): return True

# === MUTAGEN IMPORTS ===
try:
    from mutagen import File
    from mutagen.id3 import ID3
    from mutagen.oggvorbis import OggVorbis
    from mutagen.mp4 import MP4
    from mutagen.asf import ASF
    from mutagen.aiff import AIFF
    from mutagen.wave import WAVE
    from mutagen.oggopus import OggOpus
    import mutagen
    from mutagen.mp3 import MP3
    from mutagen.flac import FLAC
    MUTAGEN_AVAILABLE = True
    logger.info("Mutagen imported successfully")
except ImportError as e:
    MUTAGEN_AVAILABLE = False
    logger.error(f"Mutagen not available: {e}")
    # Mutagen is critical for music playback
    raise ImportError("Mutagen is required for music file handling")

# === ARABIC TEXT SUPPORT ===
try:
    from arabic_utils import reshape_arabic_text, contains_arabic, get_display_text, ARABIC_SUPPORT
    ARABIC_UTILS_AVAILABLE = True
    logger.info("Arabic text utilities imported successfully")
except ImportError as e:
    ARABIC_UTILS_AVAILABLE = False
    logger.warning(f"Arabic text utilities not available: {e}")
    # Define fallback functions
    def contains_arabic(text): return False
    def reshape_arabic_text(text): return text
    def get_display_text(text, always_process=False): return text
    ARABIC_SUPPORT = False

# === STANDARD LIBRARY IMPORTS ===
import traceback
import json
import re
import subprocess
from io import BytesIO
from base64 import b64decode
import hashlib
import sys
from math import cos, sin, radians

# === SAFE KV FILE LOADING ===
def safe_load_kv(filename):
    """تحميل ملف KV بشكل آمن"""
    try:
        if os.path.exists(filename):
            Builder.load_file(filename)
            logger.info(f"✅ Loaded KV file: {filename}")
            return True
        else:
            logger.warning(f"❌ KV file not found: {filename}")
            return False
    except Exception as e:
        logger.error(f"❌ Error loading KV file {filename}: {e}")
        return False

# === WINDOW SETTINGS ===
try:
    # إعدادات النافذة للأندرويد - ملء الشاشة
    Window.fullscreen = 'auto'
    logger.info("Window fullscreen set to auto")
except Exception as e:
    logger.warning(f"Failed to set window fullscreen: {e}")

# === FONT REGISTRATION ===
def register_system_font():
    """تسجيل الخط العربي بشكل آمن"""
    try:
        font_file = 'NotoNaskhArabic-VariableFont_wght.ttf'
        font_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts', font_file)

        if os.path.exists(font_path):
            logger.info(f"Found font file at: {font_path}")
            font_name = 'NotoNaskhArabic-VariableFont_wght'

            # Register the Arabic font with all styles
            LabelBase.register(
                name=font_name,
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            # Register as default font
            LabelBase.register(
                name='default',
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            # Override Roboto font
            LabelBase.register(
                name='Roboto',
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            logger.info(f"✅ Successfully registered {font_name} as the default font")
            return font_name
        else:
            logger.warning(f"❌ Font file not found at: {font_path}")
            return 'Roboto'
    except Exception as e:
        logger.error(f"❌ Error registering system font: {e}")
        return 'Roboto'

# Call the function to register system font at startup
system_font = register_system_font()

# === SAFE PERMISSION REQUEST ===
def request_permissions_safely():
    """طلب الأذونات بشكل آمن"""
    if not ANDROID_PERMISSIONS_AVAILABLE:
        logger.info("Android permissions not available, skipping permission request")
        return

    try:
        required_permissions = [
            Permission.READ_EXTERNAL_STORAGE,
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.INTERNET,
            Permission.FOREGROUND_SERVICE,
            Permission.WAKE_LOCK
        ]

        # فحص الأذونات أولاً
        missing_permissions = []
        for perm in required_permissions:
            try:
                if not check_permission(perm):
                    missing_permissions.append(perm)
            except Exception as e:
                logger.warning(f"Error checking permission {perm}: {e}")

        if missing_permissions:
            logger.info(f"Requesting missing permissions: {missing_permissions}")
            
            def permission_callback(permissions, results):
                try:
                    for perm, result in zip(permissions, results):
                        if result:
                            logger.info(f"✅ Permission granted: {perm}")
                        else:
                            logger.warning(f"❌ Permission denied: {perm}")
                except Exception as e:
                    logger.error(f"Error in permission callback: {e}")

            request_permissions(missing_permissions, permission_callback)
        else:
            logger.info("✅ All required permissions already granted")

    except Exception as e:
        logger.error(f"❌ Error requesting permissions: {e}")

# === MEMORY CLEANUP FUNCTION ===
def cleanup_memory():
    """تنظيف الذاكرة بشكل دوري"""
    try:
        import gc
        gc.collect()
        logger.debug("Memory cleanup completed")
    except Exception as e:
        logger.error(f"Error during memory cleanup: {e}")

# Schedule memory cleanup every 60 seconds
Clock.schedule_interval(lambda dt: cleanup_memory(), 60)

logger.info("🚀 Main module initialization completed successfully")
logger.info(f"📊 Available modules: CustomSlider={CUSTOM_SLIDER_AVAILABLE}, PlayingIndicator={PLAYING_INDICATOR_AVAILABLE}, AudioEnhancer={AUDIO_ENHANCER_AVAILABLE}")
logger.info(f"📊 Audio setup: {audio_setup_success}, Arabic support: {ARABIC_UTILS_AVAILABLE}")
