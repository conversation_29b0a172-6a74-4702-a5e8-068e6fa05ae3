<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="{{ args.package }}"
    android:versionCode="{{ args.numeric_version }}"
    android:versionName="{{ args.version }}">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/icon"
        android:label="{{ args.name }}"
        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
        android:hardwareAccelerated="true"
        android:debuggable="{{ args.debug }}"
        android:largeHeap="true">

        <!-- Main Activity -->
        <activity
            android:name="org.kivy.android.PythonActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="{{ args.orientation }}"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Intent filter for handling shared URLs -->
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>

            <!-- Intent filter for handling YouTube URLs -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="*.youtube.com" />
                <data android:host="youtu.be" />
                <data android:host="*.soundcloud.com" />
            </intent-filter>

            <!-- Intent filter for handling text selection -->
            <intent-filter>
                <action android:name="android.intent.action.PROCESS_TEXT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>

            <!-- Meta-data for the share target service -->
            <meta-data
                android:name="android.service.chooser.chooser_target_service"
                android:value=".ShareTargetService" />
        </activity>

        <!-- Share Target Service -->
        <service
            android:name=".ShareTargetService"
            android:permission="android.permission.BIND_CHOOSER_TARGET_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.service.chooser.ChooserTargetService" />
            </intent-filter>
        </service>

        <!-- Python Service -->
        <service android:name="org.kivy.android.PythonService"
                 android:process=":python_service"
                 android:exported="true" />

        <!-- Notification Icon -->
        <meta-data android:name="com.google.android.gms.version"
                   android:value="@integer/google_play_services_version" />

        <!-- Notification Channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="music_player_channel" />

        <!-- Notification Icon -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/icon" />

        <!-- Notification Color -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />
    </application>
</manifest>
