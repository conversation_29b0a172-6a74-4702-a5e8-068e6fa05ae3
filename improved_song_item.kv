<ImprovedSongItem>:
    size_hint_y: None
    height: dp(70)
    ripple_behavior: True
    md_bg_color: [0.95, 0.95, 0.95, 1] if app.theme_cls.theme_style == "Light" else [0.15, 0.15, 0.15, 1]
    radius: [dp(12)]
    elevation: dp(2)
    padding: [dp(10), dp(5), dp(10), dp(5)]
    
    MDBoxLayout:
        orientation: 'horizontal'
        spacing: dp(12)
        padding: [0, 0, 0, 0]
        
        # صورة الغلاف
        MDCard:
            size_hint: None, None
            size: dp(50), dp(50)
            radius: [dp(8)]
            elevation: dp(1)
            padding: 0
            pos_hint: {'center_y': 0.5}
            md_bg_color: [0.9, 0.9, 0.9, 1]
            
            AsyncImage:
                id: album_cover
                source: root.album_cover_path
                allow_stretch: True
                keep_ratio: True
        
        # معلومات الأغنية
        MDBoxLayout:
            orientation: 'vertical'
            spacing: dp(2)
            size_hint_x: 1
            pos_hint: {'center_y': 0.5}
            
            # عنوان الأغنية
            MDLabel:
                id: song_title
                text: root.title
                font_style: 'Subtitle1'
                font_name: root.font_name
                theme_text_color: "Custom"
                text_color: [0, 0.7, 0, 1] if root.is_current_song else app.root.get_text_color()
                shorten: True
                shorten_from: 'right'
                size_hint_y: None
                height: self.texture_size[1]
            
            # معلومات إضافية (المدة والحجم)
            MDBoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: dp(20)
                
                MDLabel:
                    id: song_info
                    text: root.duration
                    font_style: 'Caption'
                    font_name: root.font_name
                    theme_text_color: "Secondary"
                    size_hint_y: None
                    height: self.texture_size[1]
        
        # مؤشر الإكوالايزر (يظهر فقط للأغنية الحالية)
        MDBoxLayout:
            id: equalizer_container
            size_hint: None, None
            size: dp(30), dp(30)
            pos_hint: {'center_y': 0.5}
            opacity: 1 if root.is_current_song and app.root.is_playing else 0
            
            # سيتم إضافة رسم الإكوالايزر في الكود البرمجي
        
        # زر الإعدادات
        MDIconButton:
            id: settings_button
            icon: "dots-vertical"
            theme_text_color: "Custom"
            text_color: app.root.get_text_color()
            pos_hint: {'center_y': 0.5}
            on_release: root.show_settings_menu()
            ripple_scale: 1.5
