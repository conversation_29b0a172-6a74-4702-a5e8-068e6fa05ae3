#!/usr/bin/env python3
"""
اختبار طلب الأذونات في التطبيق
Test permission requests in the app
"""

import os
import logging
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.clock import Clock

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PermissionTestWidget(BoxLayout):
    """واجهة اختبار الأذونات"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # تحميل مدير الأذونات
        try:
            from permission_manager import permission_manager
            self.permission_manager = permission_manager
            self.permissions_available = True
        except ImportError:
            self.permissions_available = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان
        title = Label(
            text='اختبار الأذونات - Permission Test',
            size_hint_y=None,
            height=50,
            font_size=20
        )
        self.add_widget(title)
        
        # حالة الأذونات
        self.status_label = Label(
            text='جاري فحص الأذونات...\nChecking permissions...',
            size_hint_y=None,
            height=100,
            font_size=16
        )
        self.add_widget(self.status_label)
        
        # أزرار الاختبار
        buttons_layout = BoxLayout(orientation='vertical', spacing=10)
        
        # زر طلب الأذونات الأساسية
        essential_btn = Button(
            text='طلب الأذونات الأساسية\nRequest Essential Permissions',
            size_hint_y=None,
            height=60
        )
        essential_btn.bind(on_press=self.request_essential_permissions)
        buttons_layout.add_widget(essential_btn)
        
        # زر طلب جميع الأذونات
        all_btn = Button(
            text='طلب جميع الأذونات\nRequest All Permissions',
            size_hint_y=None,
            height=60
        )
        all_btn.bind(on_press=self.request_all_permissions)
        buttons_layout.add_widget(all_btn)
        
        # زر فحص الحالة
        check_btn = Button(
            text='فحص حالة الأذونات\nCheck Permission Status',
            size_hint_y=None,
            height=60
        )
        check_btn.bind(on_press=self.check_permission_status)
        buttons_layout.add_widget(check_btn)
        
        # زر اختبار الوصول للملفات
        file_test_btn = Button(
            text='اختبار الوصول للملفات\nTest File Access',
            size_hint_y=None,
            height=60
        )
        file_test_btn.bind(on_press=self.test_file_access)
        buttons_layout.add_widget(file_test_btn)
        
        self.add_widget(buttons_layout)
        
        # تفاصيل الأذونات
        self.details_label = Label(
            text='تفاصيل الأذونات ستظهر هنا...\nPermission details will appear here...',
            text_size=(None, None),
            halign='left',
            valign='top'
        )
        self.add_widget(self.details_label)
        
        # فحص أولي للأذونات
        Clock.schedule_once(self.initial_permission_check, 1)
    
    def initial_permission_check(self, dt):
        """فحص أولي للأذونات"""
        if not self.permissions_available:
            self.status_label.text = '❌ مدير الأذونات غير متاح\n❌ Permission manager not available'
            return
        
        try:
            # فحص الأذونات الأساسية
            essential_ok = self.permission_manager.check_essential_permissions()
            
            if essential_ok:
                self.status_label.text = '✅ الأذونات الأساسية متاحة\n✅ Essential permissions available'
            else:
                self.status_label.text = '⚠️ الأذونات الأساسية مفقودة\n⚠️ Essential permissions missing'
            
            # عرض تفاصيل الأذونات
            self.update_permission_details()
            
        except Exception as e:
            self.status_label.text = f'❌ خطأ في فحص الأذونات\n❌ Error checking permissions: {e}'
            logger.error(f"Error in initial permission check: {e}")
    
    def request_essential_permissions(self, instance):
        """طلب الأذونات الأساسية"""
        if not self.permissions_available:
            self.status_label.text = '❌ مدير الأذونات غير متاح\n❌ Permission manager not available'
            return
        
        self.status_label.text = '🔐 جاري طلب الأذونات الأساسية...\n🔐 Requesting essential permissions...'
        
        def callback(success, message):
            if success:
                self.status_label.text = '✅ تم منح الأذونات الأساسية\n✅ Essential permissions granted'
            else:
                self.status_label.text = f'❌ فشل في منح الأذونات\n❌ Failed to grant permissions: {message}'
            
            self.update_permission_details()
        
        try:
            self.permission_manager.request_essential_permissions_only(callback)
        except Exception as e:
            self.status_label.text = f'❌ خطأ في طلب الأذونات\n❌ Error requesting permissions: {e}'
            logger.error(f"Error requesting essential permissions: {e}")
    
    def request_all_permissions(self, instance):
        """طلب جميع الأذونات"""
        if not self.permissions_available:
            self.status_label.text = '❌ مدير الأذونات غير متاح\n❌ Permission manager not available'
            return
        
        self.status_label.text = '🔐 جاري طلب جميع الأذونات...\n🔐 Requesting all permissions...'
        
        def callback(success, message):
            if success:
                self.status_label.text = '✅ تم منح جميع الأذونات\n✅ All permissions granted'
            else:
                self.status_label.text = f'⚠️ تم منح بعض الأذونات\n⚠️ Some permissions granted: {message}'
            
            self.update_permission_details()
        
        try:
            self.permission_manager.request_all_permissions(callback)
        except Exception as e:
            self.status_label.text = f'❌ خطأ في طلب الأذونات\n❌ Error requesting permissions: {e}'
            logger.error(f"Error requesting all permissions: {e}")
    
    def check_permission_status(self, instance):
        """فحص حالة الأذونات"""
        if not self.permissions_available:
            self.status_label.text = '❌ مدير الأذونات غير متاح\n❌ Permission manager not available'
            return
        
        try:
            status = self.permission_manager.get_permission_status()
            
            granted = sum(1 for granted in status.values() if granted)
            total = len(status)
            
            self.status_label.text = f'📊 الأذونات: {granted}/{total}\n📊 Permissions: {granted}/{total}'
            self.update_permission_details()
            
        except Exception as e:
            self.status_label.text = f'❌ خطأ في فحص الحالة\n❌ Error checking status: {e}'
            logger.error(f"Error checking permission status: {e}")
    
    def update_permission_details(self):
        """تحديث تفاصيل الأذونات"""
        if not self.permissions_available:
            return
        
        try:
            status = self.permission_manager.get_permission_status()
            
            details = "تفاصيل الأذونات:\nPermission Details:\n\n"
            
            for perm, granted in status.items():
                perm_name = perm.split('.')[-1]  # Get last part of permission name
                status_icon = "✅" if granted else "❌"
                details += f"{status_icon} {perm_name}\n"
            
            self.details_label.text = details
            self.details_label.text_size = (self.width, None)
            
        except Exception as e:
            self.details_label.text = f"خطأ في عرض التفاصيل: {e}"
            logger.error(f"Error updating permission details: {e}")
    
    def test_file_access(self, instance):
        """اختبار الوصول للملفات"""
        try:
            # اختبار قراءة مجلد التخزين الخارجي
            from android.storage import primary_external_storage_path
            storage_path = primary_external_storage_path()
            
            if os.path.exists(storage_path):
                files = os.listdir(storage_path)
                file_count = len(files)
                self.status_label.text = f'✅ الوصول للملفات يعمل\n✅ File access works: {file_count} files'
            else:
                self.status_label.text = '❌ لا يمكن الوصول للتخزين\n❌ Cannot access storage'
                
        except ImportError:
            self.status_label.text = '⚠️ مكتبة التخزين غير متاحة\n⚠️ Storage library not available'
        except PermissionError:
            self.status_label.text = '❌ لا توجد أذونات للوصول\n❌ No permission to access files'
        except Exception as e:
            self.status_label.text = f'❌ خطأ في اختبار الملفات\n❌ File test error: {e}'
            logger.error(f"Error testing file access: {e}")

class PermissionTestApp(App):
    """تطبيق اختبار الأذونات"""
    
    def build(self):
        return PermissionTestWidget()
    
    def on_start(self):
        """عند بدء التطبيق"""
        logger.info("Permission test app started")

def main():
    """الدالة الرئيسية"""
    logger.info("Starting permission test app...")
    app = PermissionTestApp()
    app.run()

if __name__ == '__main__':
    main()
