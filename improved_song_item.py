import os
import logging
from random import uniform
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, BooleanProperty, NumericProperty, ObjectProperty
from kivymd.app import MDApp
from kivymd.uix.card import MDCard
from kivy.graphics import Color, RoundedRectangle
from kivy.core.audio import SoundLoader

# إعداد التسجيل
logger = logging.getLogger(__name__)

class ImprovedSongItem(MDCard):
    """عنصر أغنية محسن للشاشة الرئيسية"""
    
    # خصائص العنصر
    title = StringProperty("")
    file_path = StringProperty("")
    album_cover_path = StringProperty("")
    duration = StringProperty("0:00")
    font_name = StringProperty("NotoNaskhArabic-VariableFont_wght")
    index = NumericProperty(-1)
    is_current_song = BooleanProperty(False)
    on_item_click = ObjectProperty(None)
    
    # متغيرات للإكوالايزر
    _equalizer_event = None
    
    def __init__(self, **kwargs):
        super(ImprovedSongItem, self).__init__(**kwargs)
        self._effect_anim = None
        self._effect_size = 0
        
        # تحميل صورة الغلاف
        Clock.schedule_once(self._load_album_cover, 0.1)
        
        # بدء تأثير الإكوالايزر إذا كانت هذه هي الأغنية الحالية
        self.bind(is_current_song=self._on_is_current_song_changed)
        
    def _load_album_cover(self, dt):
        """تحميل صورة الغلاف للأغنية"""
        try:
            if not self.album_cover_path or not os.path.exists(self.album_cover_path):
                # استخدام صورة الغلاف الافتراضية
                app = MDApp.get_running_app()
                if app and hasattr(app, 'root') and hasattr(app.root, 'default_album_cover'):
                    self.album_cover_path = app.root.default_album_cover
                else:
                    # استخدام مسار افتراضي
                    self.album_cover_path = "images/default_album_cover.png"
        except Exception as e:
            logger.error(f"Error loading album cover: {e}")
    
    def _on_is_current_song_changed(self, instance, value):
        """يتم استدعاؤها عند تغيير خاصية is_current_song"""
        if value:  # إذا أصبحت هذه هي الأغنية الحالية
            self.start_equalizer_animation()
        else:  # إذا لم تعد هذه هي الأغنية الحالية
            self.stop_equalizer_animation()
    
    def start_equalizer_animation(self):
        """بدء تأثير الإكوالايزر"""
        # إيقاف أي تأثير سابق
        self.stop_equalizer_animation()
        
        # دالة لتحديث الإكوالايزر
        def update_equalizer(dt):
            app = MDApp.get_running_app()
            if not app or not hasattr(app, 'root') or not hasattr(app.root, 'is_playing'):
                return False
                
            if not app.root.is_playing or not self.is_current_song:
                return False
            
            # تحديث رسم الإكوالايزر
            equalizer_container = self.ids.equalizer_container
            with equalizer_container.canvas.after:
                equalizer_container.canvas.after.clear()
                
                # الحصول على أبعاد الحاوية
                width = equalizer_container.width
                height = equalizer_container.height
                base_x = equalizer_container.x
                base_y = equalizer_container.y
                
                # الحصول على ألوان التطبيق
                primary_color = app.root.get_primary_color() if hasattr(app.root, 'get_primary_color') else [0.3, 0.8, 1, 1]
                r, g, b = primary_color[0], primary_color[1], primary_color[2]
                
                # رسم أشرطة الإكوالايزر
                num_bars = 3
                bar_width = dp(3)
                spacing = (width - (num_bars * bar_width)) / (num_bars + 1)
                
                for i in range(num_bars):
                    # ارتفاع عشوائي لكل شريط
                    bar_height = uniform(height * 0.2, height * 0.9)
                    
                    # موقع الشريط
                    x = base_x + spacing + i * (bar_width + spacing)
                    y = base_y + (height - bar_height) / 2
                    
                    # رسم الشريط
                    Color(r, g, b, 1)
                    RoundedRectangle(
                        pos=[x, y],
                        size=[bar_width, bar_height],
                        radius=[dp(1)]
                    )
            
            return True  # استمرار التأثير
        
        # جدولة التحديث كل 0.15 ثانية
        self._equalizer_event = Clock.schedule_interval(update_equalizer, 0.15)
    
    def stop_equalizer_animation(self):
        """إيقاف تأثير الإكوالايزر"""
        if self._equalizer_event:
            self._equalizer_event.cancel()
            self._equalizer_event = None
            
            # مسح الرسم
            if hasattr(self, 'ids') and hasattr(self.ids, 'equalizer_container'):
                with self.ids.equalizer_container.canvas.after:
                    self.ids.equalizer_container.canvas.after.clear()
    
    def on_release(self):
        """يتم استدعاؤها عند النقر على العنصر"""
        if self.on_item_click:
            self.on_item_click(self.index)
    
    def show_settings_menu(self):
        """عرض قائمة إعدادات الأغنية"""
        app = MDApp.get_running_app()
        if app and hasattr(app, 'root') and hasattr(app.root, 'show_song_settings_menu'):
            app.root.show_song_settings_menu(self.ids.settings_button, self.file_path)
