"""
مدير الأذونات للتطبيق - معالجة شاملة لأذونات الأندرويد
Permission Manager - Comprehensive Android permissions handling
"""

import logging
from kivy.clock import Clock
from kivy.utils import platform

logger = logging.getLogger(__name__)

class PermissionManager:
    """مدير الأذونات مع معالجة شاملة"""
    
    def __init__(self):
        self.permissions_available = False
        self.permission_status = {}
        self.callbacks = []
        
        # تحميل مكتبات الأذونات
        self._load_permission_libraries()
        
        # قائمة الأذونات المطلوبة
        self.essential_permissions = [
            "android.permission.READ_EXTERNAL_STORAGE",
            "android.permission.WRITE_EXTERNAL_STORAGE",
            "android.permission.INTERNET"
        ]
        
        self.optional_permissions = [
            "android.permission.FOREGROUND_SERVICE",
            "android.permission.WAKE_LOCK",
            "android.permission.VIBRATE",
            "android.permission.ACCESS_NETWORK_STATE",
            "android.permission.MODIFY_AUDIO_SETTINGS"
        ]
        
        logger.info("PermissionManager initialized")
    
    def _load_permission_libraries(self):
        """تحميل مكتبات الأذونات"""
        try:
            if platform == 'android':
                from android.permissions import request_permissions, Permission, check_permission
                self.request_permissions = request_permissions
                self.Permission = Permission
                self.check_permission = check_permission
                self.permissions_available = True
                logger.info("✅ Android permissions library loaded")
            else:
                logger.info("Not running on Android - using fallback")
                self._create_fallback_functions()
        except ImportError as e:
            logger.warning(f"Android permissions not available: {e}")
            self._create_fallback_functions()
    
    def _create_fallback_functions(self):
        """إنشاء دوال بديلة للاختبار"""
        self.request_permissions = lambda perms, callback=None: None
        
        class FallbackPermission:
            READ_EXTERNAL_STORAGE = "android.permission.READ_EXTERNAL_STORAGE"
            WRITE_EXTERNAL_STORAGE = "android.permission.WRITE_EXTERNAL_STORAGE"
            INTERNET = "android.permission.INTERNET"
            FOREGROUND_SERVICE = "android.permission.FOREGROUND_SERVICE"
            WAKE_LOCK = "android.permission.WAKE_LOCK"
            VIBRATE = "android.permission.VIBRATE"
            ACCESS_NETWORK_STATE = "android.permission.ACCESS_NETWORK_STATE"
            MODIFY_AUDIO_SETTINGS = "android.permission.MODIFY_AUDIO_SETTINGS"
        
        self.Permission = FallbackPermission
        self.check_permission = lambda perm: True
        self.permissions_available = False
    
    def request_all_permissions(self, callback=None):
        """طلب جميع الأذونات المطلوبة"""
        if not self.permissions_available:
            logger.info("Permissions not available - skipping request")
            if callback:
                callback(True, "Permissions not available")
            return
        
        try:
            # فحص الأذونات الحالية
            missing_permissions = self._check_missing_permissions()
            
            if not missing_permissions:
                logger.info("✅ All permissions already granted")
                if callback:
                    callback(True, "All permissions granted")
                return
            
            logger.info(f"🔐 Requesting {len(missing_permissions)} permissions...")
            
            def permission_callback(permissions, results):
                try:
                    granted = []
                    denied = []
                    
                    for perm, result in zip(permissions, results):
                        self.permission_status[perm] = result
                        if result:
                            granted.append(perm)
                            logger.info(f"✅ Granted: {perm}")
                        else:
                            denied.append(perm)
                            logger.warning(f"❌ Denied: {perm}")
                    
                    # إشعار النتائج
                    self._notify_permission_results(granted, denied)
                    
                    # استدعاء callback المخصص
                    if callback:
                        success = len(denied) == 0
                        message = f"Granted: {len(granted)}, Denied: {len(denied)}"
                        callback(success, message)
                        
                except Exception as e:
                    logger.error(f"Error in permission callback: {e}")
                    if callback:
                        callback(False, str(e))
            
            # طلب الأذونات
            self.request_permissions(missing_permissions, permission_callback)
            
        except Exception as e:
            logger.error(f"Error requesting permissions: {e}")
            if callback:
                callback(False, str(e))
    
    def _check_missing_permissions(self):
        """فحص الأذونات المفقودة"""
        missing = []
        all_permissions = self.essential_permissions + self.optional_permissions
        
        for perm in all_permissions:
            try:
                if not self.check_permission(perm):
                    missing.append(perm)
                    logger.info(f"❌ Missing: {perm}")
                else:
                    logger.info(f"✅ Granted: {perm}")
                    self.permission_status[perm] = True
            except Exception as e:
                logger.warning(f"Error checking {perm}: {e}")
                missing.append(perm)
        
        return missing
    
    def _notify_permission_results(self, granted, denied):
        """إشعار نتائج الأذونات"""
        try:
            if denied:
                self._show_permission_denied_message(len(denied))
            
            if granted:
                self._show_permission_granted_message(len(granted))
                
        except Exception as e:
            logger.error(f"Error notifying permission results: {e}")
    
    def _show_permission_denied_message(self, count):
        """عرض رسالة الأذونات المرفوضة"""
        try:
            def show_message(dt):
                try:
                    from kivymd.uix.snackbar import Snackbar
                    message = f"تم رفض {count} من الأذونات. يمكنك تفعيلها من إعدادات التطبيق."
                    snackbar = Snackbar(
                        text=message,
                        duration=5,
                        snackbar_x="10dp",
                        snackbar_y="10dp"
                    )
                    snackbar.open()
                    logger.info(f"Showed permission denied message: {count}")
                except Exception as e:
                    logger.warning(f"Could not show snackbar: {e}")
            
            Clock.schedule_once(show_message, 1)
            
        except Exception as e:
            logger.error(f"Error showing permission denied message: {e}")
    
    def _show_permission_granted_message(self, count):
        """عرض رسالة الأذونات الممنوحة"""
        try:
            def show_message(dt):
                try:
                    from kivymd.uix.snackbar import Snackbar
                    message = f"تم منح {count} من الأذونات بنجاح!"
                    snackbar = Snackbar(
                        text=message,
                        duration=3,
                        snackbar_x="10dp",
                        snackbar_y="10dp"
                    )
                    snackbar.open()
                    logger.info(f"Showed permission granted message: {count}")
                except Exception as e:
                    logger.warning(f"Could not show snackbar: {e}")
            
            Clock.schedule_once(show_message, 0.5)
            
        except Exception as e:
            logger.error(f"Error showing permission granted message: {e}")
    
    def check_essential_permissions(self):
        """فحص الأذونات الأساسية"""
        if not self.permissions_available:
            return True  # افتراض الموافقة في بيئة غير أندرويد
        
        try:
            for perm in self.essential_permissions:
                if not self.check_permission(perm):
                    logger.warning(f"❌ Essential permission missing: {perm}")
                    return False
            
            logger.info("✅ All essential permissions granted")
            return True
            
        except Exception as e:
            logger.error(f"Error checking essential permissions: {e}")
            return False
    
    def request_essential_permissions_only(self, callback=None):
        """طلب الأذونات الأساسية فقط"""
        if not self.permissions_available:
            if callback:
                callback(True, "Permissions not available")
            return
        
        try:
            missing_essential = []
            for perm in self.essential_permissions:
                if not self.check_permission(perm):
                    missing_essential.append(perm)
            
            if not missing_essential:
                logger.info("✅ All essential permissions already granted")
                if callback:
                    callback(True, "All essential permissions granted")
                return
            
            logger.info(f"🔐 Requesting {len(missing_essential)} essential permissions...")
            
            def essential_callback(permissions, results):
                try:
                    all_granted = all(results)
                    granted_count = sum(results)
                    
                    if all_granted:
                        logger.info("✅ All essential permissions granted")
                        message = "جميع الأذونات الأساسية تم منحها!"
                    else:
                        logger.warning(f"⚠️ Only {granted_count}/{len(permissions)} essential permissions granted")
                        message = f"تم منح {granted_count} من {len(permissions)} أذونات أساسية"
                    
                    if callback:
                        callback(all_granted, message)
                        
                except Exception as e:
                    logger.error(f"Error in essential permission callback: {e}")
                    if callback:
                        callback(False, str(e))
            
            self.request_permissions(missing_essential, essential_callback)
            
        except Exception as e:
            logger.error(f"Error requesting essential permissions: {e}")
            if callback:
                callback(False, str(e))
    
    def schedule_periodic_check(self, interval=60):
        """جدولة فحص دوري للأذونات"""
        def periodic_check(dt):
            try:
                if not self.check_essential_permissions():
                    logger.warning("⚠️ Essential permissions missing - requesting again")
                    self.request_essential_permissions_only()
            except Exception as e:
                logger.error(f"Error in periodic permission check: {e}")
        
        Clock.schedule_interval(periodic_check, interval)
        logger.info(f"Scheduled periodic permission check every {interval} seconds")
    
    def get_permission_status(self):
        """الحصول على حالة جميع الأذونات"""
        status = {}
        all_permissions = self.essential_permissions + self.optional_permissions
        
        for perm in all_permissions:
            try:
                status[perm] = self.check_permission(perm) if self.permissions_available else True
            except Exception as e:
                logger.warning(f"Error checking {perm}: {e}")
                status[perm] = False
        
        return status

# إنشاء مثيل عام لمدير الأذونات
permission_manager = PermissionManager()
