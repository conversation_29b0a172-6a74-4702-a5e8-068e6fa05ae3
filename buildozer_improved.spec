[app]

# (str) Title of your application
title = ArabicPlayer

# (str) Package name
package.name = arabicplayer

# (str) Package domain (needed for android/ios packaging)
package.domain = org.arabicplayer

# (str) Source code where the main.py live
source.dir = .

# (list) Source files to include (let empty to include all the files)
source.include_exts = py,png,jpg,jpeg,kv,atlas,ogg,mp3,wav,ttf,json

# (list) List of inclusions using pattern matching
source.include_patterns = assets/*,images/*.png,images/*.jpg,fonts/*.ttf,default_covers/*.jpg,*.py,*.kv,*.json

# (list) Source files to exclude (let empty to not exclude anything)
source.exclude_exts = spec

# (list) List of directory to exclude (let empty to not exclude anything)
source.exclude_dirs = tests,bin,.buildozer,venv,__pycache__,downloads,music,temp_covers,converted_audio,album_art,album_covers

# (list) List of exclusions using pattern matching
source.exclude_patterns = license,images/*/*.jpg,*.pyc,*.pyo,*.pyd,*.so,*.dll,*.egg-info,*.zip,*.part,*.bak

# (str) Application versioning (method 1)
version = 1.0

# (str) Application entrypoint
entrypoint = main.py

# (str) Icon of the application
#icon.filename = images/default_album_cover.png

# (str) Presplash of the application
#presplash.filename = images/default_album_cover.png

# (str) Supported orientation (landscape, sensorLandscape, portrait or all)
orientation = portrait

# (bool) Indicate if the application should be fullscreen or not
fullscreen = 1

# (bool) Copy library instead of making a libpymodules.so
copy_libs = 1

# (list) Application requirements - IMPROVED AND TESTED FOR ANDROID
# Essential libraries with error handling and fallbacks
requirements = python3==3.11.6,kivy==2.3.0,kivymd==1.1.1,mutagen==1.47.0,pyjnius,android,python-bidi==0.4.2,arabic-reshaper==3.0.0,requests==2.31.0,plyer==2.1.0,certifi,pillow==10.0.1

# (list) Permissions - Essential permissions for music player
android.permissions = READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,INTERNET,FOREGROUND_SERVICE,WAKE_LOCK,ACCESS_NETWORK_STATE,MODIFY_AUDIO_SETTINGS

# (int) Target Android API, should be as high as possible.
android.api = 31

# (int) Minimum API your APK / AAB will support.
android.minapi = 21

# (int) Android SDK version to use
android.sdk = 31

# (str) Android NDK version to use
android.ndk = 25b

# (int) Android NDK API to use. This is the minimum API your app will support, it should usually match android.minapi.
android.ndk_api = 21

# (bool) Use --private data storage (True) or --dir public storage (False)
android.private_storage = True

# (bool) If True, then skip trying to update the Android sdk
android.skip_update = False

# (bool) If True, then automatically accept SDK license
android.accept_sdk_license = True

# (str) Android entry point, default is ok for Kivy-based app
android.entrypoint = org.kivy.android.PythonActivity

# (list) Gradle repositories to add {can be necessary for some android.gradle_dependencies}
android.gradle_repositories = "google()", "mavenCentral()"

# (list) Gradle dependencies to add
android.gradle_dependencies = com.google.android.material:material:1.9.0, androidx.core:core:1.10.1, androidx.appcompat:appcompat:1.6.1

# (bool) Enable AndroidX support. Enable when 'android.gradle_dependencies'
android.enable_androidx = True

# (bool) Enables Android Auto Backup feature (Android API >=23)
android.allow_backup = True

# (str) Path to the Android logcat filters to use
android.logcat_filters = *:S python:D

# (str) The Android arch to build for, choices: armeabi-v7a, arm64-v8a, x86, x86_64
android.archs = armeabi-v7a, arm64-v8a

# (int) overrides automatic versionCode computation (used in build.gradle)
android.numeric_version = 1

# (str) Android build tools version to use
android.build_tools = 31.0.0

[buildozer]

# (int) Log level (0 = error only, 1 = info, 2 = debug (with command output))
log_level = 2

# (int) Display warning if buildozer is run as root (0 = False, 1 = True)
warn_on_root = 0

# (str) Path to build artifact storage, absolute or relative to spec file
build_dir = ./.buildozer

# (str) Path to build output (i.e. .apk, .aab, .ipa) storage
bin_dir = ./bin
