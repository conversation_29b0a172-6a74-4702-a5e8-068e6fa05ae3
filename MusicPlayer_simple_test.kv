# ملف KV مبسط لاختبار الواجهة
# Simple KV file for UI testing

<MusicPlayer>:
    orientation: 'vertical'
    size_hint: 1, 1
    
    # الشريط العلوي
    MDTopAppBar:
        title: "Arabic Music Player"
        md_bg_color: [0.2, 0.6, 1, 1]  # لون أزرق
        specific_text_color: [1, 1, 1, 1]  # نص أبيض
        elevation: 4
        size_hint_y: None
        height: dp(56)
    
    # المنطقة الرئيسية
    MDBoxLayout:
        orientation: 'vertical'
        size_hint: 1, 1
        padding: dp(20)
        spacing: dp(20)
        md_bg_color: [0.95, 0.95, 0.95, 1]  # خلفية رمادية فاتحة
        
        # رسالة ترحيب
        MDLabel:
            text: 'مرحباً بك في مشغل الموسيقى العربي\\nArabic Music Player'
            font_style: 'H5'
            halign: 'center'
            theme_text_color: 'Primary'
            size_hint_y: None
            height: dp(100)
            font_name: 'NotoNaskhArabic-VariableFont_wght'
        
        # منطقة قائمة الأغاني
        MDCard:
            orientation: 'vertical'
            size_hint: 1, 1
            padding: dp(16)
            spacing: dp(10)
            elevation: 2
            radius: [dp(8)]
            
            MDLabel:
                text: 'قائمة الأغاني\\nSong List'
                font_style: 'H6'
                halign: 'center'
                theme_text_color: 'Primary'
                size_hint_y: None
                height: dp(50)
                font_name: 'NotoNaskhArabic-VariableFont_wght'
            
            ScrollView:
                size_hint: 1, 1
                do_scroll_x: False
                
                MDList:
                    id: playlist_list
                    size_hint_y: None
                    height: self.minimum_height
                    
                    # أغنية تجريبية 1
                    OneLineListItem:
                        text: "أغنية تجريبية 1 - Test Song 1"
                        font_name: 'NotoNaskhArabic-VariableFont_wght'
                        on_release: root.show_message("تم اختيار الأغنية 1")
                    
                    # أغنية تجريبية 2
                    OneLineListItem:
                        text: "أغنية تجريبية 2 - Test Song 2"
                        font_name: 'NotoNaskhArabic-VariableFont_wght'
                        on_release: root.show_message("تم اختيار الأغنية 2")
                    
                    # أغنية تجريبية 3
                    OneLineListItem:
                        text: "أغنية تجريبية 3 - Test Song 3"
                        font_name: 'NotoNaskhArabic-VariableFont_wght'
                        on_release: root.show_message("تم اختيار الأغنية 3")
    
    # الشريط السفلي
    MDCard:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(80)
        padding: dp(10)
        spacing: dp(10)
        elevation: 8
        radius: [dp(16), dp(16), 0, 0]
        md_bg_color: [0.2, 0.6, 1, 1]  # لون أزرق
        
        # صورة الغلاف
        MDCard:
            size_hint: None, None
            size: dp(60), dp(60)
            radius: [dp(30)]
            elevation: 2
            md_bg_color: [0.8, 0.8, 0.8, 1]
            
            MDLabel:
                text: "♪"
                font_size: sp(24)
                halign: 'center'
                valign: 'middle'
                theme_text_color: 'Primary'
        
        # معلومات الأغنية
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_x: 1
            spacing: dp(5)
            
            MDLabel:
                text: 'لا توجد أغنية قيد التشغيل\\nNo song playing'
                font_style: 'Subtitle1'
                theme_text_color: 'Custom'
                text_color: [1, 1, 1, 1]
                font_name: 'NotoNaskhArabic-VariableFont_wght'
                size_hint_y: None
                height: dp(40)
                halign: 'right'
            
            MDLabel:
                text: '00:00 / 00:00'
                font_style: 'Caption'
                theme_text_color: 'Custom'
                text_color: [1, 1, 1, 0.7]
                font_name: 'NotoNaskhArabic-VariableFont_wght'
                size_hint_y: None
                height: dp(20)
                halign: 'right'
        
        # أزرار التحكم
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint: None, None
            size: dp(120), dp(60)
            spacing: dp(10)
            
            MDIconButton:
                icon: 'skip-previous'
                theme_text_color: 'Custom'
                text_color: [1, 1, 1, 1]
                size_hint: None, None
                size: dp(40), dp(40)
                on_release: root.show_message("السابق")
            
            MDIconButton:
                icon: 'play'
                theme_text_color: 'Custom'
                text_color: [1, 1, 1, 1]
                size_hint: None, None
                size: dp(40), dp(40)
                md_bg_color: [1, 1, 1, 0.2]
                radius: [dp(20)]
                on_release: root.show_message("تشغيل/إيقاف")
            
            MDIconButton:
                icon: 'skip-next'
                theme_text_color: 'Custom'
                text_color: [1, 1, 1, 1]
                size_hint: None, None
                size: dp(40), dp(40)
                on_release: root.show_message("التالي")
