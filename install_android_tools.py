#!/usr/bin/env python3
"""
سكريبت لتثبيت أدوات البناء الخاصة بأندرويد يدويًا
"""

import os
import sys
import zipfile
import shutil
import subprocess
import urllib.request
import tempfile
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# المسارات
BUILDOZER_DIR = os.path.expanduser('~/.buildozer')
ANDROID_SDK_DIR = os.path.join(BUILDOZER_DIR, 'android', 'platform', 'android-sdk')
BUILD_TOOLS_DIR = os.path.join(ANDROID_SDK_DIR, 'build-tools')
PLATFORM_TOOLS_DIR = os.path.join(ANDROID_SDK_DIR, 'platform-tools')
TOOLS_DIR = os.path.join(ANDROID_SDK_DIR, 'tools')
CMDLINE_TOOLS_DIR = os.path.join(ANDROID_SDK_DIR, 'cmdline-tools')

# روابط التنزيل
SDK_TOOLS_URL = "https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip"
BUILD_TOOLS_VERSION = "30.0.3"

def download_file(url, dest_path):
    """تنزيل ملف من URL"""
    logger.info(f"تنزيل {url} إلى {dest_path}")
    try:
        urllib.request.urlretrieve(url, dest_path)
        return True
    except Exception as e:
        logger.error(f"خطأ في تنزيل {url}: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """استخراج ملف ZIP"""
    logger.info(f"استخراج {zip_path} إلى {extract_to}")
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        return True
    except Exception as e:
        logger.error(f"خطأ في استخراج {zip_path}: {e}")
        return False

def run_command(cmd, cwd=None):
    """تنفيذ أمر في سطر الأوامر"""
    logger.info(f"تنفيذ الأمر: {' '.join(cmd)}")
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            cwd=cwd
        )
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            logger.error(f"خطأ في تنفيذ الأمر: {stderr}")
            return False
        
        logger.info(f"نتيجة الأمر: {stdout}")
        return True
    except Exception as e:
        logger.error(f"خطأ في تنفيذ الأمر: {e}")
        return False

def create_directories():
    """إنشاء المجلدات اللازمة"""
    for directory in [ANDROID_SDK_DIR, BUILD_TOOLS_DIR, PLATFORM_TOOLS_DIR, TOOLS_DIR, CMDLINE_TOOLS_DIR]:
        os.makedirs(directory, exist_ok=True)

def install_sdk_tools():
    """تثبيت أدوات SDK"""
    with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
        if not download_file(SDK_TOOLS_URL, temp_file.name):
            return False
        
        # استخراج أدوات سطر الأوامر
        extract_dir = tempfile.mkdtemp()
        if not extract_zip(temp_file.name, extract_dir):
            return False
        
        # نقل الملفات إلى المجلد الصحيح
        cmdline_tools_src = os.path.join(extract_dir, 'cmdline-tools')
        if os.path.exists(cmdline_tools_src):
            latest_dir = os.path.join(CMDLINE_TOOLS_DIR, 'latest')
            os.makedirs(latest_dir, exist_ok=True)
            
            for item in os.listdir(cmdline_tools_src):
                src_path = os.path.join(cmdline_tools_src, item)
                dst_path = os.path.join(latest_dir, item)
                
                if os.path.isdir(src_path):
                    shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(src_path, dst_path)
        
        # تنظيف
        os.unlink(temp_file.name)
        shutil.rmtree(extract_dir)
        
        return True

def install_build_tools():
    """تثبيت أدوات البناء"""
    # تعيين متغيرات البيئة
    env = os.environ.copy()
    env['ANDROID_HOME'] = ANDROID_SDK_DIR
    
    # تحديث مدير الحزم
    sdkmanager_path = os.path.join(CMDLINE_TOOLS_DIR, 'latest', 'bin', 'sdkmanager')
    if not os.path.exists(sdkmanager_path):
        logger.error(f"لم يتم العثور على sdkmanager في {sdkmanager_path}")
        return False
    
    # جعل الملف قابل للتنفيذ
    os.chmod(sdkmanager_path, 0o755)
    
    # تثبيت أدوات البناء
    cmd = [
        sdkmanager_path,
        f"build-tools;{BUILD_TOOLS_VERSION}",
        "platform-tools",
        f"platforms;android-30",
        "--sdk_root=" + ANDROID_SDK_DIR
    ]
    
    # قبول التراخيص
    yes_cmd = "yes | " + " ".join(cmd)
    os.system(yes_cmd)
    
    return True

def fix_permissions():
    """إصلاح أذونات الملفات"""
    for root, dirs, files in os.walk(ANDROID_SDK_DIR):
        for d in dirs:
            os.chmod(os.path.join(root, d), 0o755)
        for f in files:
            file_path = os.path.join(root, f)
            if os.access(file_path, os.X_OK):
                os.chmod(file_path, 0o755)
            else:
                os.chmod(file_path, 0o644)

def main():
    """الدالة الرئيسية"""
    logger.info("بدء تثبيت أدوات أندرويد")
    
    # إنشاء المجلدات
    create_directories()
    
    # تثبيت أدوات SDK
    if not install_sdk_tools():
        logger.error("فشل تثبيت أدوات SDK")
        return False
    
    # تثبيت أدوات البناء
    if not install_build_tools():
        logger.error("فشل تثبيت أدوات البناء")
        return False
    
    # إصلاح الأذونات
    fix_permissions()
    
    logger.info("تم تثبيت أدوات أندرويد بنجاح")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
