#!/usr/bin/env python3
"""
مشغل الموسيقى العربي - نسخة مقاومة للتحطم
Arabic Music Player - Crash-resistant version
"""

import os
import sys
import logging
import traceback

# إعداد السجلات مع معالجة الأخطاء
def setup_logging():
    """إعداد نظام السجلات بشكل آمن"""
    try:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)
    except Exception as e:
        print(f"Failed to setup logging: {e}")
        return None

logger = setup_logging()

def crash_handler(exc_type, exc_value, exc_traceback):
    """معالج التحطم العام"""
    try:
        if logger:
            logger.error("💥 CRASH DETECTED!")
            logger.error(f"Exception Type: {exc_type.__name__}")
            logger.error(f"Exception Value: {exc_value}")
            logger.error("Traceback:")
            logger.error(''.join(traceback.format_tb(exc_traceback)))
        else:
            print(f"💥 CRASH: {exc_type.__name__}: {exc_value}")
    except:
        print("💥 CRITICAL CRASH - Unable to log details")

# تعيين معالج التحطم
sys.excepthook = crash_handler

if logger:
    logger.info("🚀 Starting Arabic Music Player - Crash-resistant version")

# === SAFE KIVY SETUP ===
def setup_kivy_safely():
    """إعداد Kivy بشكل آمن"""
    try:
        # تعيين متغيرات البيئة قبل استيراد Kivy
        os.environ['KIVY_NO_CONSOLELOG'] = '1'
        os.environ['KIVY_LOG_LEVEL'] = 'warning'
        
        # استيراد Kivy
        import kivy
        kivy.require('2.0.0')
        
        from kivy.config import Config
        
        # إعدادات أساسية
        Config.set('graphics', 'resizable', '0')
        Config.set('kivy', 'log_level', 'warning')
        
        if logger:
            logger.info(f"✅ Kivy {kivy.__version__} loaded successfully")
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"❌ Failed to setup Kivy: {e}")
        return False

# إعداد Kivy
kivy_success = setup_kivy_safely()
if not kivy_success:
    print("❌ Critical: Kivy setup failed")
    sys.exit(1)

# === SAFE IMPORTS ===
def safe_import(module_name, fallback=None):
    """استيراد آمن للمكتبات"""
    try:
        module = __import__(module_name)
        if logger:
            logger.info(f"✅ {module_name} imported successfully")
        return module
    except ImportError as e:
        if logger:
            logger.warning(f"⚠️ {module_name} not available: {e}")
        return fallback
    except Exception as e:
        if logger:
            logger.error(f"❌ Error importing {module_name}: {e}")
        return fallback

# استيراد المكتبات الأساسية
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.slider import Slider
from kivy.uix.progressbar import ProgressBar
from kivy.clock import Clock
from kivy.core.audio import SoundLoader
from kivy.core.window import Window

# استيراد KivyMD (اختياري)
kivymd = safe_import('kivymd')
if kivymd:
    try:
        from kivymd.app import MDApp
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.label import MDLabel
        from kivymd.uix.button import MDRaisedButton, MDIconButton
        USE_KIVYMD = True
        if logger:
            logger.info("✅ KivyMD components loaded")
    except Exception as e:
        USE_KIVYMD = False
        if logger:
            logger.warning(f"⚠️ KivyMD components failed: {e}")
else:
    USE_KIVYMD = False

# استيراد مكتبات إضافية (اختيارية)
mutagen = safe_import('mutagen')
requests = safe_import('requests')

# === ANDROID PERMISSIONS (SAFE) ===
def setup_android_permissions():
    """إعداد أذونات الأندرويد بشكل آمن"""
    try:
        from android.permissions import request_permissions, Permission
        
        def request_permissions_safe():
            try:
                permissions = [
                    Permission.READ_EXTERNAL_STORAGE,
                    Permission.WRITE_EXTERNAL_STORAGE,
                    Permission.INTERNET
                ]
                request_permissions(permissions)
                if logger:
                    logger.info("🔐 Android permissions requested")
            except Exception as e:
                if logger:
                    logger.warning(f"⚠️ Permission request failed: {e}")
        
        # طلب الأذونات بعد ثانية
        Clock.schedule_once(lambda dt: request_permissions_safe(), 1.0)
        return True
        
    except ImportError:
        if logger:
            logger.info("ℹ️ Running on desktop - no Android permissions needed")
        return False
    except Exception as e:
        if logger:
            logger.error(f"❌ Android permissions setup failed: {e}")
        return False

# === SIMPLE MUSIC PLAYER WIDGET ===
class SimpleMusicPlayer(BoxLayout):
    """مشغل موسيقى بسيط ومقاوم للتحطم"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # متغيرات التشغيل
        self.current_sound = None
        self.is_playing = False
        self.playlist = []
        self.current_index = 0
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل قائمة الأغاني
        Clock.schedule_once(self.load_music_files, 1.0)
        
        if logger:
            logger.info("✅ SimpleMusicPlayer initialized")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        try:
            # عنوان التطبيق
            title = Label(
                text='🎵 مشغل الموسيقى العربي\nArabic Music Player',
                size_hint_y=None,
                height=80,
                font_size=20,
                halign='center'
            )
            self.add_widget(title)
            
            # معلومات الأغنية الحالية
            self.song_label = Label(
                text='لا توجد أغنية محددة\nNo song selected',
                size_hint_y=None,
                height=60,
                font_size=16
            )
            self.add_widget(self.song_label)
            
            # شريط التقدم
            self.progress_bar = ProgressBar(
                max=100,
                value=0,
                size_hint_y=None,
                height=20
            )
            self.add_widget(self.progress_bar)
            
            # أزرار التحكم
            controls_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=60,
                spacing=10
            )
            
            # زر السابق
            prev_btn = Button(text='⏮️ السابق\nPrevious', font_size=14)
            prev_btn.bind(on_press=self.previous_song)
            controls_layout.add_widget(prev_btn)
            
            # زر تشغيل/إيقاف
            self.play_btn = Button(text='▶️ تشغيل\nPlay', font_size=14)
            self.play_btn.bind(on_press=self.toggle_playback)
            controls_layout.add_widget(self.play_btn)
            
            # زر التالي
            next_btn = Button(text='⏭️ التالي\nNext', font_size=14)
            next_btn.bind(on_press=self.next_song)
            controls_layout.add_widget(next_btn)
            
            self.add_widget(controls_layout)
            
            # شريط مستوى الصوت
            volume_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=40,
                spacing=10
            )
            
            volume_label = Label(text='🔊', size_hint_x=None, width=40)
            volume_layout.add_widget(volume_label)
            
            self.volume_slider = Slider(
                min=0, max=1, value=0.7,
                size_hint_y=None, height=40
            )
            self.volume_slider.bind(value=self.on_volume_change)
            volume_layout.add_widget(self.volume_slider)
            
            self.add_widget(volume_layout)
            
            # حالة التطبيق
            self.status_label = Label(
                text='جاري البحث عن الأغاني...\nSearching for music files...',
                size_hint_y=None,
                height=40,
                font_size=12
            )
            self.add_widget(self.status_label)
            
            if logger:
                logger.info("✅ UI setup completed")
                
        except Exception as e:
            if logger:
                logger.error(f"❌ UI setup failed: {e}")
    
    def load_music_files(self, dt):
        """تحميل ملفات الموسيقى"""
        try:
            music_extensions = ['.mp3', '.wav', '.ogg', '.m4a']
            music_dirs = ['music', 'downloads', '.']
            
            self.playlist = []
            
            for music_dir in music_dirs:
                if os.path.exists(music_dir):
                    for file in os.listdir(music_dir):
                        if any(file.lower().endswith(ext) for ext in music_extensions):
                            full_path = os.path.join(music_dir, file)
                            self.playlist.append(full_path)
            
            if self.playlist:
                self.status_label.text = f'تم العثور على {len(self.playlist)} أغنية\nFound {len(self.playlist)} songs'
                self.update_song_info()
            else:
                self.status_label.text = 'لم يتم العثور على أغاني\nNo music files found'
            
            if logger:
                logger.info(f"✅ Loaded {len(self.playlist)} music files")
                
        except Exception as e:
            self.status_label.text = f'خطأ في تحميل الأغاني\nError loading music: {str(e)[:50]}'
            if logger:
                logger.error(f"❌ Failed to load music files: {e}")
    
    def update_song_info(self):
        """تحديث معلومات الأغنية الحالية"""
        try:
            if self.playlist and 0 <= self.current_index < len(self.playlist):
                current_file = self.playlist[self.current_index]
                filename = os.path.basename(current_file)
                self.song_label.text = f'🎵 {filename}\n({self.current_index + 1}/{len(self.playlist)})'
            else:
                self.song_label.text = 'لا توجد أغنية\nNo song'
        except Exception as e:
            if logger:
                logger.error(f"❌ Failed to update song info: {e}")
    
    def toggle_playback(self, instance):
        """تبديل التشغيل/الإيقاف"""
        try:
            if self.is_playing:
                self.stop_playback()
            else:
                self.start_playback()
        except Exception as e:
            if logger:
                logger.error(f"❌ Playback toggle failed: {e}")
            self.status_label.text = f'خطأ في التشغيل\nPlayback error: {str(e)[:30]}'
    
    def start_playback(self):
        """بدء التشغيل"""
        try:
            if not self.playlist:
                self.status_label.text = 'لا توجد أغاني للتشغيل\nNo songs to play'
                return
            
            current_file = self.playlist[self.current_index]
            
            # إيقاف التشغيل الحالي
            if self.current_sound:
                self.current_sound.stop()
            
            # تحميل وتشغيل الأغنية
            self.current_sound = SoundLoader.load(current_file)
            
            if self.current_sound:
                self.current_sound.volume = self.volume_slider.value
                self.current_sound.play()
                self.is_playing = True
                self.play_btn.text = '⏸️ إيقاف\nPause'
                self.status_label.text = '▶️ يتم التشغيل...\nPlaying...'
                
                if logger:
                    logger.info(f"✅ Playing: {os.path.basename(current_file)}")
            else:
                self.status_label.text = 'فشل في تحميل الأغنية\nFailed to load song'
                if logger:
                    logger.error(f"❌ Failed to load: {current_file}")
                
        except Exception as e:
            self.status_label.text = f'خطأ في التشغيل\nPlayback error: {str(e)[:30]}'
            if logger:
                logger.error(f"❌ Start playback failed: {e}")
    
    def stop_playback(self):
        """إيقاف التشغيل"""
        try:
            if self.current_sound:
                self.current_sound.stop()
            
            self.is_playing = False
            self.play_btn.text = '▶️ تشغيل\nPlay'
            self.status_label.text = '⏸️ متوقف\nStopped'
            
            if logger:
                logger.info("✅ Playback stopped")
                
        except Exception as e:
            if logger:
                logger.error(f"❌ Stop playback failed: {e}")
    
    def previous_song(self, instance):
        """الأغنية السابقة"""
        try:
            if self.playlist:
                self.current_index = (self.current_index - 1) % len(self.playlist)
                self.update_song_info()
                if self.is_playing:
                    self.start_playback()
        except Exception as e:
            if logger:
                logger.error(f"❌ Previous song failed: {e}")
    
    def next_song(self, instance):
        """الأغنية التالية"""
        try:
            if self.playlist:
                self.current_index = (self.current_index + 1) % len(self.playlist)
                self.update_song_info()
                if self.is_playing:
                    self.start_playback()
        except Exception as e:
            if logger:
                logger.error(f"❌ Next song failed: {e}")
    
    def on_volume_change(self, instance, value):
        """تغيير مستوى الصوت"""
        try:
            if self.current_sound:
                self.current_sound.volume = value
        except Exception as e:
            if logger:
                logger.error(f"❌ Volume change failed: {e}")

# === APPLICATION CLASS ===
class CrashResistantMusicApp(App):
    """تطبيق مشغل الموسيقى المقاوم للتحطم"""
    
    def build(self):
        """بناء التطبيق"""
        try:
            if logger:
                logger.info("🏗️ Building app...")
            
            # إعداد النافذة
            Window.clearcolor = (0.1, 0.1, 0.1, 1)
            
            # إنشاء الواجهة الرئيسية
            main_widget = SimpleMusicPlayer()
            
            if logger:
                logger.info("✅ App built successfully")
            
            return main_widget
            
        except Exception as e:
            if logger:
                logger.error(f"❌ App build failed: {e}")
            
            # إنشاء واجهة طوارئ
            emergency_layout = BoxLayout(orientation='vertical', padding=20)
            error_label = Label(
                text=f'خطأ في بناء التطبيق\nApp build error:\n{str(e)[:100]}',
                font_size=16
            )
            emergency_layout.add_widget(error_label)
            return emergency_layout
    
    def on_start(self):
        """عند بدء التطبيق"""
        try:
            if logger:
                logger.info("🚀 App starting...")
            
            # إعداد أذونات الأندرويد
            setup_android_permissions()
            
            # إعداد ملء الشاشة للأندرويد
            if hasattr(Window, 'fullscreen'):
                Window.fullscreen = 'auto'
            
            if logger:
                logger.info("✅ App started successfully")
                
        except Exception as e:
            if logger:
                logger.error(f"❌ App start failed: {e}")
    
    def on_stop(self):
        """عند إغلاق التطبيق"""
        try:
            if logger:
                logger.info("🛑 App stopping...")
            
            # تنظيف الذاكرة
            import gc
            gc.collect()
            
            if logger:
                logger.info("✅ App stopped cleanly")
                
        except Exception as e:
            if logger:
                logger.error(f"❌ App stop failed: {e}")

# === MAIN FUNCTION ===
def main():
    """الدالة الرئيسية"""
    try:
        if logger:
            logger.info("🎵 Starting Arabic Music Player - Crash Resistant Version")
        
        app = CrashResistantMusicApp()
        app.run()
        
        if logger:
            logger.info("✅ App finished successfully")
        
    except Exception as e:
        if logger:
            logger.error(f"💥 CRITICAL ERROR: {e}")
            logger.error(f"Traceback:\n{traceback.format_exc()}")
        else:
            print(f"💥 CRITICAL ERROR: {e}")

if __name__ == '__main__':
    main()
