#!/usr/bin/env python3
"""
إصلاح مشكلة NDK وبناء APK
Fix NDK issue and build APK
"""

import os
import shutil
import subprocess
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        logger.info(f"🔄 {description}: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            logger.error(f"❌ {description} failed")
            if result.stderr:
                logger.error(f"Error: {result.stderr[:500]}...")
            return False, result.stderr
    except Exception as e:
        logger.error(f"❌ Exception in {description}: {e}")
        return False, str(e)

def fix_ndk_issue():
    """إصلاح مشكلة NDK"""
    logger.info("🔧 Fixing NDK version issue...")
    
    print("=" * 60)
    print("🎯 NDK Fix: Updating to minimum supported version (25b)")
    print("=" * 60)
    
    # نسخ الإعدادات المحدثة
    if os.path.exists('buildozer_ndk25.spec'):
        try:
            # نسخ احتياطية من buildozer.spec الحالي
            if os.path.exists('buildozer.spec'):
                shutil.copy('buildozer.spec', 'buildozer_backup.spec')
                logger.info("✅ Created backup of current buildozer.spec")
            
            shutil.copy('buildozer_ndk25.spec', 'buildozer.spec')
            logger.info("✅ Updated buildozer.spec with NDK 25b")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to update buildozer.spec: {e}")
            return False
    else:
        logger.error("❌ buildozer_ndk25.spec not found")
        return False

def clean_and_build():
    """تنظيف وبناء APK"""
    logger.info("🧹 Cleaning and building APK...")
    
    # تنظيف البيئة
    logger.info("🔄 Cleaning buildozer environment...")
    
    # حذف مجلد .buildozer
    if os.path.exists('.buildozer'):
        try:
            shutil.rmtree('.buildozer')
            logger.info("✅ Removed .buildozer directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove .buildozer: {e}")
    
    # حذف مجلد bin
    if os.path.exists('bin'):
        try:
            shutil.rmtree('bin')
            logger.info("✅ Removed bin directory")
        except Exception as e:
            logger.error(f"❌ Failed to remove bin: {e}")
    
    # تشغيل buildozer clean
    success, output = run_command("buildozer android clean", "Buildozer clean")
    
    # بناء APK
    logger.info("🏗️ Building APK with NDK 25b...")
    success, output = run_command("buildozer android debug", "Building APK")
    
    if success:
        logger.info("🎉 APK built successfully!")
        
        # البحث عن ملف APK
        apk_files = []
        if os.path.exists('bin'):
            for file in os.listdir('bin'):
                if file.endswith('.apk'):
                    apk_files.append(os.path.join('bin', file))
        
        if apk_files:
            for apk in apk_files:
                size = os.path.getsize(apk) / (1024 * 1024)  # MB
                logger.info(f"📱 APK created: {apk} ({size:.2f} MB)")
        
        return True
    else:
        logger.error("❌ APK build failed")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 Starting NDK fix and APK build process...")
    print("🎯 Issue: NDK 23c is not supported, minimum is NDK 25b")
    print()
    
    # الخطوة 1: إصلاح مشكلة NDK
    print("📋 Step 1: Fixing NDK version...")
    if not fix_ndk_issue():
        print("❌ Failed to fix NDK issue")
        return
    
    # الخطوة 2: تنظيف وبناء
    print("\n📋 Step 2: Cleaning and building APK...")
    success = clean_and_build()
    
    if success:
        print("\n🎉 SUCCESS! APK created successfully!")
        print("📱 Check the 'bin' folder for your APK file")
        print("🚀 Ready for testing on Android device!")
        print("\n📋 What was fixed:")
        print("   ✅ Updated NDK from 23c to 25b (minimum supported)")
        print("   ✅ Updated API to 31 for compatibility")
        print("   ✅ Used minimal requirements for stability")
        print("   ✅ Single architecture (arm64-v8a) for simplicity")
    else:
        print("\n❌ Build failed. Possible next steps:")
        print("1. Check if you have enough disk space")
        print("2. Try with even fewer requirements:")
        print("   requirements = python3,kivy,pyjnius,android")
        print("3. Check the full build log for specific errors")
        print("4. Try building on a different platform (Ubuntu instead of Colab)")

if __name__ == "__main__":
    main()
